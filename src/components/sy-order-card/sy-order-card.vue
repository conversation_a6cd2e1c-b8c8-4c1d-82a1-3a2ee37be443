<template>
  <view class="sy-order-card-container bg-white rounded-16rpx mx-24rpx mb-24rpx overflow-hidden">
    <!-- 卡片上半部分渐变背景区域 -->

    <view
      class="sy-order-card-gradient-bg absolute top-0 left-0 right-0 h-378rpx"
      :style="{
        opacity: 0.2,
        background: orderData?.channelColor?.gradient || '#fff',
      }"
    ></view>

    <!-- 水印区域 -->
    <view class="sy-order-card-watermark absolute top-80rpx right-0 z-1">
      <view
        :class="['iconfont', orderData?.channelColor?.watermarkIconClass || 'icon-xiafancai']"
        :style="{
          fontSize: '210rpx',
          color: orderData?.channelColor?.watermarkColor || '#F0F0F0',
          opacity: 0.5,
        }"
      ></view>
    </view>

    <!-- 订单头部信息 -->
    <view
      class="sy-order-card-header flex items-center justify-between px-24rpx pt-24rpx relative z-1"
    >
      <view class="flex items-center">
        <image
          class="sy-order-card-channel-icon w-48rpx h-48rpx mr-16rpx"
          :src="orderData?.merchant?.avatar || ''"
          mode="aspectFill"
        />
        <text class="text-30rpx font-medium max-w-480rpx">
          {{ orderData?.merchant?.name || orderData?.merchant?.alias || '' }}
        </text>
      </view>
      <!-- 渠道类型标签 -->
      <view class="sy-order-card-type-tag px-12rpx py-4rpx rounded-4rpx">
        <text
          class="text-24rpx font-medium"
          :style="{
            color: '#F33429',
          }"
        >
          {{ orderData?.type || '' }}
        </text>
      </view>
    </view>

    <!-- 虚线分割线 -->
    <view class="sy-order-card-divider mx-24rpx mt-16rpx relative z-1"></view>

    <!-- 第二行信息：租户标签 -->
    <view class="flex items-center px-24rpx mt-16rpx relative z-1">
      <!-- 添加租户名称标签 -->
      <view
        class="sy-order-card-tenant-tag rounded-4rpx h-32rpx flex items-center justify-center pl-8rpx pr-8rpx"
        :style="{
          background:
            orderData?.channelColor?.bg || getChannelButtonStyle(orderData?.saleChannel).bg,
        }"
      >
        <image
          src="/static/images/icons/tent_tag.svg"
          class="w-24rpx h-24rpx mr-4rpx"
          mode="aspectFit"
          :style="{
            filter: `${orderData?.channelColor?.watermarkColor ? `drop-shadow(0 0 0 ${orderData.channelColor.watermarkColor})` : ''}`,
          }"
        />
        <text class="text-20rpx text-white font-medium">
          {{ orderData?.appName || '外卖' }}
        </text>
      </view>
    </view>

    <!-- 第三行信息：订单流水号、取餐号、送达时间、第三方流水号和状态 -->
    <view class="flex items-baseline justify-between px-24rpx mt-16rpx relative z-1">
      <view class="flex items-baseline flex-1" v-if="orderData?.bizType != '20'">
        <!-- 取餐号 -->
        <view class="mr-16rpx flex items-baseline">
          <text class="sy-order-card-serial-prefix text-30rpx font-semibold text-#222222 mr-4rpx">
            #
          </text>
          <text class="sy-order-card-serial-number text-64rpx font-semibold text-#222222">
            {{ orderData?.takeNo || '' }}
          </text>
          <view class="sy-order-card-type-tag-yu rounded-6rpx" v-if="orderData?.isBook == 2">
            <text class="text-22rpx font-medium">预</text>
          </view>
        </view>
        <view v-if="orderData?.pickupNumber" class="mr-16rpx">
          <text class="text-26rpx opacity-80">取餐号：</text>
          <text class="text-30rpx font-medium">{{ orderData?.pickupNumber }}</text>
        </view>
        <text class="text-22rpx">
          {{ orderData?.deliveryTime }}
          <text>前送达</text>
        </text>
      </view>
      <view class="items-baseline flex-1" v-if="orderData?.bizType == '20'">
        <!-- 取餐号 -->
        <view class="mr-16rpx flex">
          <text class="text-34rpx opacity-80">取餐码</text>
          <text class="text-34rpx font-medium text-#F33429">{{ orderData?.takeNo }}</text>
          <view class="sy-order-card-type-tag-yu" v-if="orderData?.isBook == 2">
            <text class="text-22rpx font-medium">预</text>
          </view>
        </view>

        <view class="text-22rpx">
          {{ orderData?.deliveryTime ? orderData?.deliveryTime + '前取餐' : '' }}
        </view>
      </view>

      <!-- 订单状态 -->
      <view class="flex items-center ml-16rpx">
        <text
          class="text-26rpx mr-16rpx"
          :class="{ 'text-#F33429 fw-500': orderData?.orderStatus.code === 'CANCEL' }"
        >
          {{ orderData?.status || '' }}
        </text>
        <!-- 待接单状态显示接单按钮 -->
        <SyAuth
          code="jidanbao_menu_index_order_acceptbtn"
          toast-mode="toast"
          v-slot="{ isAllowed, onNoPermissionClick, disableStyle, disableClass }"
        >
          <view
            v-if="showAcceptButton"
            class="sy-order-card-action-btn rounded-26rpx flex items-center justify-center"
            :class="disableClass"
            :style="{
              backgroundColor:
                orderData?.channelColor?.btnBg ||
                orderData?.channelColor?.bg ||
                getChannelButtonStyle(orderData?.saleChannel).bg,
              color:
                orderData?.channelColor?.btnText ||
                getChannelButtonStyle(orderData?.saleChannel).text,
              width: '132rpx',
              height: '52rpx',
              ...disableStyle,
            }"
            @click="isAllowed ? onAcceptOrderClick() : onNoPermissionClick()"
          >
            <text
              class="text-24rpx font-medium"
              :style="{
                color:
                  orderData?.channelColor?.btnText ||
                  getChannelButtonStyle(orderData?.saleChannel).text,
              }"
            >
              立即接单
            </text>
          </view>
        </SyAuth>
      </view>
    </view>

    <!-- 虚线分割线 -->
    <view class="sy-order-card-divider mx-24rpx mt-32rpx relative z-1"></view>

    <!-- 售后模块 (仅在售后模式下显示) -->
    <view
      v-if="isAfterSaleMode && hasAfterSaleData"
      class="sy-order-card-after-sale bg-#F5F6F7 px-24rpx pt-32rpx pb-40rpx relative z-1"
    >
      <!-- 1. 顶部标题栏 + 步骤条区域 -->
      <view class="after-sale-header flex items-center justify-between mb-32rpx">
        <text class="text-32rpx font-600 text-#222222">售后</text>

        <!--<view class="after-sale-steps flex items-center w-60%">
          <wd-steps :active="0" align-center custom-class="compact-steps w-100% mb-18rpx">
            <wd-step custom-class="compact-steps-item" title="1.退款处理" />
            <wd-step custom-class="compact-steps-item" title="2.餐损申请" />
            <wd-step custom-class="compact-steps-item" title="3.按责赔付" />
          </wd-steps>
        </view> -->
      </view>

      <!-- 2. 主要信息区域 - 显示所有售后记录 -->
      <view class="after-sale-main-wrapper">
        <view
          v-for="(record, index) in sortedAfterSaleRecords"
          :key="index"
          class="after-sale-item-view"
        >
          <view
            class="after-sale-item"
            :class="{
              'after-sale-item-line': index < sortedAfterSaleRecords.length - 1,
            }"
          >
            <view class="after-sale-record-item flex mb-32rpx">
              <!-- 左侧手写步骤条 -->
              <view v-if="sortedAfterSaleRecords.length > 1">
                <image
                  v-if="
                    index === 0 && record.refundStatus && ['30', '50'].includes(record.refundStatus)
                  "
                  :src="'/static/images/icons/cg_icon.png'"
                  class="w-24rpx h-24rpx status-img"
                  mode="aspectFit"
                />
                <image
                  v-if="
                    index === 0 &&
                    record.refundStatus &&
                    !['30', '50'].includes(record.refundStatus)
                  "
                  :src="'/static/images/icons/dq_icon.png'"
                  class="w-24rpx h-24rpx status-img"
                  mode="aspectFit"
                />
                <image
                  v-if="index !== 0"
                  :src="'/static/images/icons/zj_cion.png'"
                  class="w-8rpx h-8rpx zj-img"
                  mode="aspectFit"
                />
              </view>
              <view
                class="custom-step-wrapper flex flex-col items-center"
                v-if="sortedAfterSaleRecords.length > 1"
              >
                <!-- 步骤图标 -->
                <!-- <view class="step-icon-container">
                  <wd-icon
                    v-if="index === 0"
                    name="check-circle-filled"
                    color="#00C37F"
                    size="24rpx"
                  />
                  <wd-icon v-else name="circle" size="8rpx" color="#333333" />
                </view> -->

                <!-- 连接线（除了最后一个） -->
                <!-- <view
                  v-if="index < sortedAfterSaleRecords.length - 1"
                  class="step-connector-line w-2rpx bg-#d9d9d9 mt-8rpx"
                ></view> -->
              </view>

              <!-- 右侧内容区域 -->
              <view class="step-content-area w-100% h-100% flex justify-between">
                <view class="w-70%">
                  <!-- 标题信息 -->
                  <view v-if="record.title" class="title-info mb-4rpx pl-16rpx">
                    <text
                      class="title-text title-text-other text-28rpx text-#222222"
                      :class="{
                        'title-text-one':
                          index === 0 &&
                          record.refundStatus &&
                          ['30', '50'].includes(record.refundStatus),
                      }"
                    >
                      {{ record.title }}
                    </text>
                  </view>
                  <!-- 理由说明 -->
                  <view v-if="record.reason" class="reason-info mb-4rpx pl-16rpx">
                    <text
                      class="reason-text text-24rpx text-#3D3D3D"
                      style="word-break: break-word; white-space: normal"
                    >
                      {{ record.reason }}
                    </text>
                  </view>
                </view>
                <view>
                  <!-- 状态信息行 -->
                  <view class="title-info title-info-view mb-4rpx pl-16rpx">
                    <text
                      class="title-text text-26rpx text-#222222 font-400"
                      v-if="record.refundStatusName"
                      style="font-weight: 400"
                    >
                      {{ record.refundStatusName }}
                    </text>
                    <text
                      class="title-text title-text-view text-22rpx text-#666666"
                      style="font-weight: 400"
                      v-if="!record.refundStatusName"
                    >
                      {{ formatTime(record.createTime) }}
                    </text>
                  </view>
                  <view class="status-text flex items-center mb-4rpx" v-if="0">
                    <!-- <view class="status-dot1 w-12rpx h-12rpx rounded-full mr-16rpx"></view> -->
                    <text
                      class="title-text text-26rpx font-400"
                      v-if="index === 0"
                      style="font-weight: 400"
                    >
                      <!-- {{record.refundStatusName == '审核失败'? '拒绝退款': record.refundStatusName || '状态未知'}} -->
                      {{ record.refundStatusName }}
                    </text>
                    <text
                      class="title-text status-text text-22rpx text-#666666 h-100%"
                      v-if="index !== 0"
                    >
                      {{ formatTime(record.createTime) }}
                    </text>
                  </view>
                  <!-- 时间信息 -->
                  <view class="time-info pl-28rpx" v-if="record.refundStatusName">
                    <text class="time-text text-22rpx text-#666666">
                      {{ formatTime(record.createTime) }}
                    </text>
                  </view>
                </view>
              </view>
            </view>
            <!-- 售后上传图片 -->
            <view v-if="record.url && record.url.length > 0">
              <view
                class="flex flex-wrap mb-40rpx"
                :class="{
                  'gap-8rpx pl-38rpx': sortedAfterSaleRecords.length > 1,
                  'gap-20rpx': sortedAfterSaleRecords.length <= 1,
                }"
              >
                <image
                  v-for="image in record.url"
                  :key="image"
                  :src="image"
                  class="w-136rpx h-136rpx rounded-16rpx"
                  mode="aspectFit"
                  @click="previewAfterSaleImage(image, record.url)"
                />
              </view>
            </view>

            <!-- 商品信息表格 - 基于当前记录 -->
            <!-- <view
            v-if="record?.afterSalesItems && record.afterSalesItems.length > 0"
            class="after-sale-items mb-40rpx"
            :class="{
              'px-38rpx': sortedAfterSaleRecords.length > 1,
            }"
          >
            <wd-table
              custom-class="custom-wd-table bg-transparent"
              :data="record?.afterSalesItems"
              :stripe="false"
              :height="400"
              :border="true"
              :style="{
                backgroundColor: 'transparent !important',
                borderColor: '#ccc',
              }"
            >
              <wd-table-col
                width="388rpx"
                align="center"
                prop="itemName"
                label="商品名称"
              ></wd-table-col>
              <wd-table-col
                width="120rpx"
                align="center"
                prop="itemNum"
                label="数量"
              ></wd-table-col>
              <wd-table-col
                width="120rpx"
                align="center"
                prop="payTotalAmount"
                label="金额"
              ></wd-table-col>
            </wd-table>
          </view> -->
            <view
              v-if="record?.afterSalesItems && record.afterSalesItems.length > 0"
              class="after-sale-items mb-40rpx"
              :class="{
                'px-38rpx': sortedAfterSaleRecords.length > 1,
              }"
            >
              <!-- 使用SyTable替换原有表格 -->
              <sy-table
                :data="record.afterSalesItems"
                :columns="afterSaleColumns"
                :border="true"
                :max-visible-rows="0"
                fixed-header-mode="rows"
                :scrollable="false"
                :fixed-header="true"
              />
            </view>
            <!-- <view
            v-if="record?.afterSalesItems && record.afterSalesItems.length > 0"
            class="after-sale-items mb-40rpx"
            :class="{
              'px-38rpx': sortedAfterSaleRecords.length > 1,
            }"
          >
            <sy-table
              :data="record.afterSalesItems"
              :columns="afterSaleColumns"
              :border="true"
              :scrollable="false"
              :fixed-header="false"
            />
          </view> -->

            <!-- 操作按钮区域 -->
            <SyAuth
              code="jidanbao_menu_index_order_afterSaleActionsbtn"
              toast-mode="toast"
              v-slot="{ isAllowed, onNoPermissionClick }"
            >
              <view
                v-if="
                  record.refundStatus === '10' &&
                  ['TikTokMiniProgram', 'DouYinXiaoshiDa', 'JingDongTakeOutBrand'].includes(
                    orderData.extlChannel,
                  )
                "
                class="after-sale-actions flex gap-24rpx mb-40rpx px-40rpx"
              >
                <!-- 拒绝按钮 -->
                <view
                  class="reject-btn flex-1 h-88rpx flex items-center justify-center bg-white border-2rpx border-#e8e8e8 rounded-44rpx"
                  :class="{ 'sy-auth-no-permission': !isAllowed }"
                  :style="{
                    filter: !isAllowed ? 'grayscale(1)' : '',
                    opacity: !isAllowed ? 0.5 : 1,
                  }"
                  @tap="
                    isAllowed
                      ? onAfterSaleReject(record.tradeNo || '', orderData)
                      : onNoPermissionClick()
                  "
                >
                  <text class="text-32rpx font-500 text-#666666">拒绝</text>
                </view>
                <!-- 同意按钮 -->
                <view
                  class="approve-btn flex-1 h-88rpx flex items-center justify-center bg-#f33429 rounded-44rpx"
                  :class="{ 'sy-auth-no-permission': !isAllowed }"
                  :style="{
                    filter: !isAllowed ? 'grayscale(1)' : '',
                    opacity: !isAllowed ? 0.5 : 1,
                  }"
                  @tap="
                    isAllowed
                      ? onAfterSaleApprove(record.tradeNo || '', orderData)
                      : onNoPermissionClick()
                  "
                >
                  <text class="text-32rpx font-500 text-white">同意</text>
                </view>
              </view>
            </SyAuth>
          </view>
        </view>
      </view>

      <!-- 3. URL链接区域 - 基于当前记录 -->
      <!-- <view
        v-if="currentAfterSaleRecord?.url"
        class="after-sale-url mb-32rpx p-24rpx bg-#f0f7ff rounded-12rpx border border-#e1f0ff"
      >
        <view class="flex items-center">
          <text
            class="text-28rpx text-#1890ff flex-1 break-all"
            @tap="openUrl(currentAfterSaleRecord.url)"
          >
            {{ currentAfterSaleRecord.url }}
          </text>
        </view>
      </view> -->

      <!-- 4. 商品信息表格 - 基于当前记录 -->
      <!-- <view
        v-if="
          currentAfterSaleRecord?.afterSalesItems &&
          currentAfterSaleRecord.afterSalesItems.length > 0
        "
        class="after-sale-items mb-40rpx"
      >
        <wd-table
          custom-class="custom-wd-table bg-transparent"
          :data="transformedAfterSalesItems"
          :stripe="false"
          :height="400"
          :border="true"
          :style="{
            backgroundColor: 'transparent !important',
          }"
        >
          <wd-table-col width="240rpx" prop="itemName" label="商品名称"></wd-table-col>
          <wd-table-col prop="itemNum" label="数量"></wd-table-col>
          <wd-table-col prop="payTotalAmount" label="金额"></wd-table-col>
        </wd-table>
      </view> -->

      <!-- 5. 操作按钮区域 -->
      <view
        v-if="
          shouldShowActions &&
          !['TikTokMiniProgram', 'DouYinXiaoshiDa', 'JingDongTakeOutBrand'].includes(
            orderData.extlChannel,
          )
        "
        class="after-sale-actions flex gap-24rpx"
      >
        <!-- 拒绝按钮 -->
        <view
          class="reject-btn flex-1 h-88rpx flex items-center justify-center bg-white border-2rpx border-#e8e8e8 rounded-44rpx"
          @tap="
            onAfterSaleReject(
              orderData?.afterSale[orderData.afterSale.length - 1]?.tradeNo || '',
              orderData,
            )
          "
        >
          <text class="text-32rpx font-500 text-#666666">拒绝</text>
        </view>

        <!-- 同意按钮 -->
        <view
          class="approve-btn flex-1 h-88rpx flex items-center justify-center bg-#f33429 rounded-44rpx"
          @tap="
            onAfterSaleApprove(
              orderData?.afterSale[orderData.afterSale.length - 1]?.tradeNo || '',
              orderData,
            )
          "
        >
          <text class="text-32rpx font-500 text-white">同意</text>
        </view>
      </view>
    </view>

    <!-- 虚线分割线 (仅在售后模式下且有售后信息时显示) -->
    <view
      v-if="isAfterSaleMode && orderData?.afterSale"
      class="sy-order-card-divider mx-24rpx mt-32rpx relative z-1"
    ></view>

    <!-- 顾客信息 -->
    <view
      class="sy-order-card-customer flex items-center justify-between px-24rpx pt-32rpx relative z-1"
    >
      <view class="flex-1">
        <text class="text-34rpx font-medium text-#333333 mr-16rpx">
          {{ orderData?.customer?.name || '未知用户' }}
        </text>
        <text class="text-22rpx text-#666666" v-if="orderData?.customer?.lastFourPhone">
          手机尾号 {{ orderData?.customer?.lastFourPhone }}
        </text>
        <text class="text-22rpx text-#666666" v-else>手机尾号 未知</text>
      </view>
      <view
        v-if="orderData?.customer?.phone"
        class="sy-order-card-call-btn w-60rpx h-60rpx rounded-full flex items-center justify-center"
        :class="canMakePhoneCall ? 'bg-#F1F1F1' : 'bg-#E0E0E0'"
        :style="{ opacity: canMakePhoneCall ? 1 : 0.5 }"
        @click="onCallClick"
      >
        <image
          src="/static/images/img/phone.svg"
          class="w-32rpx h-32rpx"
          mode="aspectFit"
          :style="{ opacity: canMakePhoneCall ? 1 : 0.6 }"
        />
      </view>
    </view>

    <!-- 地址信息 -->
    <view
      class="sy-order-card-address px-24rpx mt-16rpx"
      @click="onAddressClick"
      v-if="orderData?.bizType != '20'"
    >
      <text class="text-22rpx text-#999999 line-clamp-2">
        <!-- &lt; -->
        <!-- 使用本地计算的距离值 -->
        {{ calculatedDistance.length > 0 ? calculatedDistance + '  |' : '' }}
        {{ orderData?.address?.detail || '' }}
      </text>
    </view>

    <!-- 虚线分割线 -->
    <view v-if="!shouldHideOrderStatus" class="sy-order-card-divider mx-24rpx mt-32rpx"></view>

    <!-- 订单状态信息 -->
    <view v-if="!shouldHideOrderStatus" class="sy-order-card-status px-24rpx pt-32rpx">
      <view class="flex items-center justify-between">
        <!-- 新增：已上报出餐完成，出餐用时 -->
        <!-- <template v-if="shouldShowMealTime"> -->
        <text class="text-34rpx font-medium" v-if="orderData?.orderStatus?.mealReadyDuration">
          已上报出餐完成，出餐用时
          <text class="text-#F33429">
            {{ orderData?.orderStatus?.mealReadyDuration || '--' }}
          </text>
        </text>
        <!-- </template> -->
        <!-- 备餐中 -->
        <template
          v-else-if="
            (orderData.isBook !== 2 && orderData?.orderStatus?.code === 'IN_PREPARE') ||
            (orderData.isBook === 2 && isNearlyOrder)
          "
        >
          <text class="text-34rpx font-medium">
            备餐中，已用时
            <text class="text-#F33429">
              {{
                orderData?.isBook !== 2
                  ? formatUsedTime(orderData?.merchantConfirmTime || orderData?.times?.acceptTime)
                  : startTime(orderData?.sendTime)
              }}
            </text>
          </text>
          <!-- 待出餐状态显示上报出餐按钮 -->
          <SyAuth
            code="jidanbao_menu_index_order_reportMealsbtn"
            toast-mode="toast"
            v-slot="{ isAllowed, onNoPermissionClick }"
          >
            <view
              v-if="showReportButton"
              class="sy-order-card-action-btn rounded-26rpx flex items-center justify-center"
              :style="{
                backgroundColor: canReportMeals
                  ? orderData?.channelColor?.btnBg ||
                    orderData?.channelColor?.bg ||
                    getChannelButtonStyle(orderData?.saleChannel).bg
                  : '#CCCCCC',
                color:
                  orderData?.channelColor?.btnText ||
                  getChannelButtonStyle(orderData?.saleChannel).text,
                width: '136rpx',
                height: '52rpx',
                opacity: canReportMeals ? (isAllowed ? 1 : 0.5) : 0.7,
                filter: !isAllowed ? 'grayscale(1)' : '',
                pointerEvents: 'auto',
              }"
              :class="{ 'sy-auth-no-permission': !isAllowed }"
              @click="isAllowed && canReportMeals ? onReportOrderClick() : onNoPermissionClick()"
            >
              <text
                class="text-24rpx font-medium"
                :style="{
                  color: canReportMeals
                    ? orderData?.channelColor?.btnText ||
                      getChannelButtonStyle(orderData?.saleChannel).text
                    : '#fff',
                }"
              >
                上报出餐
              </text>
            </view>
          </SyAuth>
        </template>
        <!-- 其他状态 -->
        <template v-else>
          <text class="text-34rpx font-medium">
            {{
              orderData?.isBook !== 2
                ? '未上报出餐完成，无出餐用时'
                : !isNearlyOrder
                  ? '备餐提醒后开始计时'
                  : ''
            }}
          </text>
        </template>
      </view>
      <text
        v-if="orderData?.orderStatus.code === 'PAYED'"
        class="text-22rpx text-#666666 mt-8rpx line-clamp-2"
      >
        您有新的订单，请及时接单
      </text>
    </view>

    <!-- 配送信息区域 -->
    <template
      v-if="
        isDeliveryOrder &&
        props.orderData.extlChannel != 'DouYinXiaoshiDa' &&
        !shouldHideDeliveryInfo
      "
    >
      <!-- 当有配送信息且为外卖订单时显示 -->
      <view class="px-24rpx pt-32rpx pb-16rpx" v-if="orderData?.deliveryRecord">
        <!-- 配送信息分割线 -->
        <view class="sy-order-card-divider mx-0 mb-32rpx"></view>
        <!-- 第一块：配送状态 -->
        <view class="flex items-center justify-between mb-16rpx">
          <view @click.stop="openTrackPopup" class="flex items-center">
            <text class="text-34rpx font-medium text-#222222">
              {{ orderData?.deliveryRecord?.statusName || '--' }}
              <wd-icon name="arrow-right" size="30rpx"></wd-icon>
            </text>
            <image
              v-if="orderData?.deliveryRecord?.statusIcon"
              :src="orderData?.deliveryRecord?.statusIcon"
              class="w-24rpx h-24rpx ml-8rpx"
              mode="aspectFit"
            />
          </view>

          <!-- 拨打电话按钮 -->
          <view
            v-if="orderData?.deliveryRecord?.courierMobile"
            class="sy-order-card-call-btn w-60rpx h-60rpx rounded-full flex items-center justify-center"
            :class="canMakePhoneCall ? 'bg-#F1F1F1' : 'bg-#E0E0E0'"
            :style="{ opacity: canMakePhoneCall ? 1 : 0.5 }"
            @click="onCallRiderClick"
          >
            <image
              src="/static/images/img/phone.svg"
              class="w-32rpx h-32rpx"
              mode="aspectFit"
              :style="{ opacity: canMakePhoneCall ? 1 : 0.6 }"
            />
          </view>
        </view>

        <!-- 第二块：骑手名称和配送渠道 -->
        <view class="flex items-center mb-16rpx">
          <text class="text-30rpx text-#222222 mr-16rpx">
            {{ getRiderName() || '--' }}
          </text>
          <wd-tag
            custom-class="px-8rpx text-20rpx py-4rpx"
            v-if="orderData?.deliveryRecord?.channelName"
            color="#9E6D35"
            bg-color="#9E6D35"
            plain
          >
            {{ orderData?.deliveryRecord?.channelName || '--' }}
          </wd-tag>
        </view>

        <!-- 第三块：送达时间和状态 -->
        <view class="flex items-center">
          <text class="text-22rpx text-#222222">
            {{ getDeliveryTimeAndStatus() || '--' }}
          </text>
        </view>
      </view>

      <!-- 当没有配送信息但处于备餐完成或已接单状态且为外卖订单时，显示待骑手接单信息 -->
      <view class="px-24rpx pt-32rpx pb-16rpx" v-else-if="shouldShowWaitingRiderInfo">
        <!-- 配送信息分割线 -->
        <view class="sy-order-card-divider mx-0 mb-32rpx"></view>

        <!-- 待骑手接单状态 -->
        <view class="flex flex-col">
          <text @click.stop="openTrackPopup" class="text-34rpx font-medium text-#222222 mb-16rpx">
            待骑手接单
            <wd-icon name="arrow-right" size="30rpx"></wd-icon>
          </text>
          <text class="text-22rpx text-#222222">
            出餐完成后分配骑手模式生效中，上报出餐后开始分配骑手
          </text>
        </view>
      </view>
      <!-- 待出餐状态-写死商家已接单 -->
      <view
        class="px-24rpx pt-32rpx pb-16rpx"
        v-else-if="orderData?.orderStatus.code === 'IN_PREPARE'"
      >
        <!-- 配送信息分割线 -->
        <view class="sy-order-card-divider mx-0 mb-32rpx"></view>

        <!-- 待骑手接单状态 -->
        <view class="flex flex-col">
          <text @click.stop="openTrackPopup" class="text-34rpx font-medium text-#222222 mb-16rpx">
            商家已接单
            <wd-icon name="arrow-right" size="30rpx"></wd-icon>
          </text>
          <text class="text-22rpx text-#222222">请在出餐完成时上报</text>
        </view>
      </view>
      <!-- 订单已取消兼容 -->
      <view class="px-24rpx pt-32rpx pb-16rpx" v-else-if="orderData?.orderStatus.code === 'CANCEL'">
        <!-- 配送信息分割线 -->
        <view class="sy-order-card-divider mx-0 mb-32rpx"></view>

        <!-- 待骑手接单状态 -->
        <view class="flex flex-col">
          <text @click.stop="openTrackPopup" class="text-34rpx font-medium text-#222222 mb-16rpx">
            {{ orderData?.status }}
            <wd-icon name="arrow-right" size="30rpx"></wd-icon>
          </text>
          <text class="text-22rpx text-#222222">您的订单已取消</text>
        </view>
      </view>
      <!-- 待接单状态 -->
      <view class="px-24rpx pt-32rpx pb-16rpx" v-else>
        <!-- 配送信息分割线 -->
        <view class="sy-order-card-divider mx-0 mb-32rpx"></view>

        <!-- 待骑手接单状态 -->
        <view class="flex flex-col">
          <text @click.stop="openTrackPopup" class="text-34rpx font-medium text-#222222 mb-16rpx">
            {{ orderData?.status }}
            <wd-icon name="arrow-right" size="30rpx"></wd-icon>
          </text>
          <text v-if="orderData?.orderStatus.code === 'PAYED'" class="text-22rpx text-#222222">
            您有新的订单，请及时接单
          </text>
        </view>
      </view>
      <!-- 操作指引按钮 -->
      <view class="guide-btn-box">
        <SyGuideBtn
          :status="props.orderData.deliveryRecord?.status || ''"
          type="orderGuid"
          :channle="props.orderData.extlChannel"
          :orderStatus="props.orderData.orderStatus.code"
        />
      </view>
    </template>
    <!-- 虚线分割线 -->
    <view class="sy-order-card-divider mx-24rpx mt-32rpx"></view>
    <!-- 备注信息 -->
    <view
      v-if="orderData?.buyerRemark"
      class="sy-order-card-remark sy-order-card-remark-color mx-24rpx mt-32rpx relative z-1"
    >
      <view
        class="w-44rpx h-30rpx px-8rpx py-4rpx mt-4rpx"
        style="background: #f33429; border-radius: 4rpx"
      >
        <text class="text-22rpx fw-500 text-#fff">备注</text>
      </view>
      <view class="ml-16rpx flex-1">
        <text class="text-30rpx fw-600 text-#222222">{{ orderData?.buyerRemark || '' }}</text>
      </view>
    </view>
    <!-- 商品信息 -->
    <view class="sy-order-card-goods px-24rpx pt-32rpx">
      <view class="flex items-center justify-between" @click="toggleExpand">
        <text class="text-34rpx font-medium text-#333333">
          {{ orderData?.goods?.count || 0 }}种商品，共{{
            orderData?.goods?.items?.reduce((total, item) => total + item.count, 0) || 0
          }}件
        </text>
        <!-- 收起状态下显示商家收入 -->
        <view v-if="!isExpanded" class="flex items-center">
          <!-- 商家收入 -->
          <view class="flex items-center">
            <text class="text-24rpx text-#666666 mr-16rpx">商家收入</text>
            <text class="text-40rpx font-medium text-#F33429">
              ¥{{ displayNum(orderData?.fees?.merchantIncome || 0) }}
            </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 展开的订单详情区域 -->
    <view v-if="isExpanded" class="sy-order-card-details">
      <!-- 虚线分割线 -->
      <view class="sy-order-card-divider mx-24rpx mt-32rpx"></view>

      <!-- 商品详情 -->
      <view class="sy-order-card-detail-item px-24rpx pt-32rpx">
        <!-- 商品列表 -->
        <view
          v-if="orderData?.goods?.items && orderData?.goods?.items.length > 0"
          class="sy-order-card-goods-list"
        >
          <!-- 商品项循环 -->
          <view
            v-for="(item, index) in orderData?.goods?.items"
            :key="index"
            class="sy-order-card-goods-item flex justify-between mb-12rpx"
          >
            <view class="flex-1">
              <view class="flex items-center">
                <text class="text-28rpx text-#222222 fw-500 max-w-480rpx name-width">
                  {{ item.name }}
                </text>
                <view class="flex items-center price-box">
                  <text
                    class="text-26rpx mr-16rpx nums-box"
                    :style="{
                      color: item.count > 1 ? '#FA6B00' : null,
                    }"
                  >
                    x{{ item.count }}
                  </text>
                  <text class="text-26rpx text-right">
                    ¥{{ displayNum(item.price * item.count) }}
                  </text>
                </view>
                <!-- 赠品标识 -->
                <view v-if="item.isGift" class="ml-8rpx px-8rpx py-2rpx bg-#F33429 rounded-4rpx">
                  <text class="text-20rpx text-white">赠品</text>
                </view>
              </view>

              <!-- 套餐内商品 -->
              <view v-if="item.combos && item.combos.length > 0" class="mt-4rpx">
                <view
                  v-for="(combo, comboIndex) in item.combos"
                  :key="comboIndex"
                  class="flex text-24rpx items-center justify-between mt-16rpx"
                >
                  <text class="text-#666666 line-clamp-2 max-w-480rpx">
                    {{ combo.name }}
                  </text>
                  <view class="flex items-center">
                    <text class="text-#666666 mr-8rpx">x{{ combo.count }}</text>
                    <text class="text-#666666 w-80rpx text-right">
                      ¥{{ displayNum(combo.addPrice) }}
                    </text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 无商品数据时的备用显示 -->
        <view v-else class="sy-order-card-goods-empty py-16rpx">
          <text class="text-28rpx text-#999999">{{ orderData?.goods?.summary }}</text>
        </view>
      </view>

      <!-- 虚线分割线 -->
      <view class="sy-order-card-divider mx-24rpx mt-32rpx"></view>

      <!-- 费用明细 -->
      <view class="sy-order-card-fee-list px-24rpx pt-32rpx">
        <!-- 商品合计 -->
        <view class="sy-order-card-fee-item flex items-center justify-between mb-16rpx">
          <text class="text-28rpx text-#666666">商品合计</text>
          <text class="text-28rpx text-#333333">¥{{ displayNum(calculateProductTotal()) }}</text>
        </view>

        <!-- 配送费或专送服务费 -->
        <view class="sy-order-card-fee-item flex items-center justify-between mb-16rpx">
          <text v-if="orderData?.delivery?.needCustomerDelivery" class="text-28rpx text-#666666">
            专送服务费
          </text>
          <text v-else class="text-28rpx text-#666666">配送费</text>
          <text class="text-28rpx text-#333333">
            ¥{{ displayNum(orderData?.fees?.deliveryFee || 0) }}
          </text>
        </view>

        <!-- 餐盒费 -->
        <view class="sy-order-card-fee-item flex items-center justify-between mb-16rpx">
          <text class="text-28rpx text-#666666">餐盒费</text>
          <text class="text-28rpx text-#333333">
            ¥{{ displayNum(orderData?.fees?.packagingFee || 0) }}
          </text>
        </view>

        <!-- 优惠金额 -->
        <view class="sy-order-card-fee-item flex items-center justify-between mb-16rpx">
          <text class="text-28rpx text-#666666">实际优惠总额</text>
          <text class="text-28rpx">¥{{ displayNum(calculateRealDiscount()) }}</text>
        </view>

        <!-- 补贴 -->
        <view class="sy-order-card-fee-item flex items-center justify-between mb-16rpx">
          <text class="text-28rpx text-#666666">补贴</text>
          <text class="text-28rpx">
            {{ displayNum(orderData?.fees?.subsidy || 0) != '0.00' ? '-' : '' }}¥{{
              displayNum(orderData?.fees?.subsidy || 0)
            }}
          </text>
        </view>

        <!-- 服务费 -->
        <view class="sy-order-card-fee-item flex items-center justify-between mb-16rpx">
          <text class="text-28rpx text-#666666">服务费</text>
          <text class="text-28rpx text-#333333">
            ¥{{ displayNum(orderData?.fees?.serviceFee || 0) }}
          </text>
        </view>

        <!-- 佣金 -->
        <view class="sy-order-card-fee-item flex items-center justify-between mb-16rpx">
          <text class="text-28rpx text-#666666">佣金</text>
          <text class="text-28rpx text-#333333">
            ¥{{ displayNum(orderData?.fees?.commission || 0) }}
          </text>
        </view>

        <!-- 细线分割线 -->
        <!-- <view class="h-1rpx bg-#EEEEEE my-16rpx"></view> -->

        <!-- 顾客支付 -->
        <view class="sy-order-card-fee-item flex items-center justify-between mb-16rpx">
          <text class="text-28rpx text-#666666">顾客实付</text>
          <text class="text-28rpx text-#333333">¥{{ displayNum(calculatePayAmount()) }}</text>
        </view>
      </view>

      <!-- 展开状态下显示商家收入 -->
      <view
        v-if="isExpanded"
        class="sy-order-card-merchant-income flex justify-end px-24rpx pt-32rpx pb-32rpx"
      >
        <view>
          <!--class="flex items-center justify-between"-->
          <text class="text-32rpx font-medium text-#333333">商家收入</text>
          <text class="text-40rpx font-medium text-#F33429">
            ¥{{ displayNum(orderData?.fees?.merchantIncome || 0) }}
          </text>
        </view>
      </view>

      <!-- 虚线分割线 -->
      <view class="sy-order-card-divider mx-24rpx mt-32rpx"></view>
      <!-- 下单时间 -->
      <view class="sy-order-card-detail-item px-24rpx pt-32rpx text-#999999 text-22rpx">
        <view class="flex items-center">
          <text>下单：</text>
          <text>
            {{ orderData?.times?.placeTime || orderData?.placeTime || '--' }}
          </text>
        </view>
      </view>

      <!-- 渠道单号 -->
      <view v-if="orderData?.channelOrderNo" class="sy-order-card-detail-item px-24rpx pt-16rpx">
        <view class="flex items-center text-#999999 text-22rpx">
          <text>渠道单号：</text>
          <view class="flex items-center">
            <text>{{ orderData?.channelOrderNo }}</text>
            <view
              class="sy-order-card-copy-btn ml-16rpx px-12rpx py-6rpx rounded-8rpx flex items-center justify-center"
              @click="onCopyChannelOrderNo"
            >
              <text class="text-#333333 fw-500">复制</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 虚线分割线 -->
    <view class="sy-order-card-divider mx-24rpx mt-32rpx"></view>

    <!-- 底部信息 -->
    <view class="sy-order-card-footer flex items-center justify-between px-24rpx py-32rpx">
      <!-- 左侧退款按钮 -->
      <SyAuth
        code="jidanbao_menu_index_order_refundbtn"
        toast-mode="toast"
        v-slot="{ isAllowed, onNoPermissionClick, disableStyle, disableClass }"
      >
        <view
          v-if="showRefundButton && !isAfterSaleMode"
          class="sy-order-card-refund-btn px-24rpx py-10rpx rounded-32rpx flex items-center"
          :class="disableClass"
          :style="disableStyle"
          @click="isAllowed ? onRefundOrderClick() : onNoPermissionClick()"
        >
          <text class="text-26rpx text-#666666">退款</text>
        </view>
      </SyAuth>
      <view
        v-if="!isNearlyOrder && orderData?.isBook && isCancelable && false"
        class="sy-order-card-refund-btn px-24rpx py-10rpx rounded-32rpx flex items-center"
        @click="onRefundOrderClick"
      >
        <text class="text-26rpx text-#666666">取消订单</text>
      </view>
      <view v-else></view>

      <!-- 右侧展开/收起按钮 -->
      <view v-if="showExpandButton" class="flex items-center" @click="toggleExpand">
        <text class="text-26rpx text-#666666 mr-8rpx">
          {{ isExpanded ? '收起' : '展开' }}完整信息
        </text>

        <text
          :class="[
            'text-20rpx text-#999999',
            isExpanded ? 'sy-order-card-collapse-arrow' : 'sy-order-card-expand-arrow',
          ]"
        >
          >
        </text>
      </view>
    </view>
  </view>

  <!-- 订单跟踪弹窗 -->
  <wd-popup
    v-model="showTrackPopup"
    position="bottom"
    :close-on-click-modal="true"
    custom-style="border-radius:24rpx 24rpx 0 0;"
  >
    <view class="track-popup-container">
      <!-- 弹窗头部 -->
      <view class="track-popup-header flex justify-center items-center px-24rpx py-20rpx">
        <text class="text-30rpx font-semibold text-#222222">订单跟踪</text>
        <view class="track-popup-close" @click="showTrackPopup = false">
          <wd-icon name="close" size="36rpx" color="#333333"></wd-icon>
        </view>
      </view>

      <!-- 弹窗内容 -->
      <view class="track-popup-content px-32rpx py-24rpx">
        <wd-steps :active="activeStep" vertical dot>
          <wd-step
            v-for="(step, index) in orderTrackSteps"
            :key="index"
            :title="step.title"
            :description="step.description"
            :status="step.status"
          >
            <template #title>
              <view class="flex justify-between items-center">
                <text
                  :class="[
                    'step-title',
                    index === orderTrackSteps.length - 1 ? 'text-#222222' : 'text-#CCCCCC',
                  ]"
                >
                  {{ step.title }}
                </text>
                <text
                  :class="[
                    'step-time',
                    index === orderTrackSteps.length - 1 ? 'text-#222222' : 'text-#CCCCCC',
                  ]"
                >
                  {{ step.time }}
                </text>
              </view>
            </template>
          </wd-step>
        </wd-steps>
      </view>
      <!-- 底部提示信息 -->
      <view class="track-popup-tips bg-#FDEAE9">
        <text class="text-26rpx text-#F33429">如无法看到配送记录，请到各平台商家APP查看</text>
      </view>

      <!-- 底部安全区域：tabbar页面需要额外预留tabbar高度（80rpx），非tabbar页面只需要基础安全区（0） -->
      <view class="track-popup-safe-area" :style="{ height: isTabbarPage ? '80rpx' : '0' }" />
    </view>
  </wd-popup>

  <!-- 拨打骑手电话弹窗 -->
  <wd-popup
    v-model="showCallRiderPopup"
    position="bottom"
    :close-on-click-modal="true"
    custom-style="border-radius:24rpx 24rpx 0 0;"
  >
    <view class="call-rider-popup-container">
      <!-- 头部提示区域 -->
      <view class="call-rider-popup-header bg-#FDEAE9 px-58rpx py-16rpx">
        <text class="text-26rpx text-#F33429 text-center">
          如果联系不上骑手，可以联系站长或站长助理
        </text>
      </view>

      <!-- 联系人信息区域 -->
      <view class="call-rider-popup-content px-32rpx py-48rpx bg-white">
        <!-- 骑手信息 -->
        <view class="flex items-center mb-32rpx">
          <!-- 左侧头像 -->
          <view class="rider-avatar-container w-104rpx h-104rpx mr-32rpx">
            <image
              src="/static/images/img/rider-avatar.svg"
              class="w-104rpx h-104rpx rounded-full bg-#FFE8E6"
              mode="aspectFill"
            />
          </view>

          <!-- 中间联系人信息 -->
          <view class="flex-1">
            <text class="text-28rpx font-medium text-#333333 block mb-12rpx">
              {{ getRiderName() || '骑手' }}
            </text>
            <text class="text-26rpx text-#999999">
              {{ formatRiderPhone(getRiderMobile()) }}
            </text>
          </view>

          <!-- 右侧拨打电话按钮 -->
          <view
            class="call-btn bg-#F33429 px-18rpx py-8rpx rounded-26rpx"
            @click="onCallRiderFromPopup"
          >
            <text class="text-24rpx text-white font-medium">拨打电话</text>
          </view>
        </view>

        <!-- 分割线 -->
        <view class="h-2rpx bg-#F0F0F0 mb-24rpx"></view>

        <!-- 联系站长提示 -->
        <view class="flex items-center justify-between">
          <text class="text-24rpx text-#666666">联系站长/站长助理</text>
          <text class="text-24rpx text-#666666">请到对应平台APP【订单详情】电话联系</text>
        </view>
      </view>

      <!-- 底部操作按钮区域 -->
      <view class="call-rider-popup-footer bg-#F1F1F1 px-24rpx py-18rpx">
        <view
          class="cancel-btn w-full h-88rpx bg-transparent flex items-center justify-center"
          @click="showCallRiderPopup = false"
        >
          <text class="text-32rpx text-#F33429 font-medium">取消</text>
        </view>
      </view>

      <!-- 底部安全区域：tabbar页面需要额外预留tabbar高度（80rpx），非tabbar页面只需要基础安全区（0rpx） -->
      <view
        class="call-rider-popup-safe-area"
        :style="{ height: isTabbarPage ? '80rpx' : '0rpx' }"
      />
    </view>
  </wd-popup>

  <!-- 拨打隐私号客户电话弹窗 -->
  <wd-popup
    v-model="showCallCustomerPopup"
    position="center"
    :close-on-click-modal="true"
    custom-style="border-radius:24rpx;width:640rpx;"
  >
    <view class="call-customer-popup-container">
      <!-- 弹窗头部 -->
      <view
        class="call-customer-popup-header flex justify-center items-center px-32rpx py-32rpx relative"
      >
        <text class="text-32rpx font-semibold text-#222222">联系用户</text>
        <view class="call-customer-popup-close" @click="showCallCustomerPopup = false">
          <wd-icon name="close" size="32rpx" color="#999999"></wd-icon>
        </view>
      </view>

      <!-- 联系人信息区域 -->
      <view class="call-customer-popup-content px-32rpx pb-32rpx bg-white">
        <!-- 隐私号信息显示 -->
        <view class="flex flex-col items-center">
          <!-- 隐私号显示 -->
          <text class="text-32rpx font-medium text-#333333 mb-32rpx">
            {{
              formatCustomerPrivacyPhone(
                props.orderData?.customer?.fullPhone || props.orderData?.customer?.phone || '',
              )
            }}
          </text>

          <!-- 提示文案 -->
          <text class="text-26rpx text-#666666 mb-24rpx text-center">
            点击【呼叫】，拨通后手动输入分机号：
          </text>

          <!-- 分机号显示 -->
          <text class="text-48rpx font-bold text-#F33429 mb-48rpx">
            {{
              getCustomerPhoneExtension(
                props.orderData?.customer?.fullPhone || props.orderData?.customer?.phone || '',
              )
            }}
          </text>

          <!-- 操作按钮区域 -->
          <view class="flex gap-24rpx w-full">
            <!-- 取消按钮 -->
            <view
              class="cancel-btn flex-1 h-88rpx bg-#F8F8F8 flex items-center justify-center rounded-44rpx"
              @click="showCallCustomerPopup = false"
            >
              <text class="text-32rpx text-#666666 font-medium">取消</text>
            </view>

            <!-- 呼叫按钮 -->
            <view
              class="call-btn flex-1 h-88rpx bg-#F33429 flex items-center justify-center rounded-44rpx"
              @click="onCallCustomerFromPopup"
            >
              <text class="text-32rpx text-white font-medium">呼叫</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </wd-popup>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted, computed } from 'vue'
import { componentProps, type OrderCardEmits, type OrderData, type OrderTrackStep } from './card'
import { getChannelColor, channelColorMap } from '@/utils/transform'
import { reportMeals, confirmOrder, fetchOrderStatusTime } from '@/service/order'
import { useShopStore } from '@/store/shop'
import {
  isPrivacyPhone,
  formatPrivacyPhone,
  getPrivacyPhoneExtension,
  getPrivacyPhoneMain,
} from '@/utils'
import { SyTable, type TableColumn } from '@/components/sy-table'
import { isOutOneHour } from '@/utils/datetime'
import SyAuth from '@/components/sy-auth'
// import SyGuideBtn from '@/components/sy-guide-btn/sy-guide-btn.vue'

// 定义组件属性
const props = defineProps(componentProps)

// 定义组件事件
const emit = defineEmits<OrderCardEmits>()

/**
 * 订单展开状态（完全由组件内部管理，默认 false）
 */
const isExpanded = ref(false)

/**
 * 订单跟踪弹窗显示状态
 */
const showTrackPopup = ref(false)

/**
 * 拨打骑手电话弹窗显示状态
 */
const showCallRiderPopup = ref(false)

/**
 * 拨打隐私号客户电话弹窗显示状态
 */
const showCallCustomerPopup = ref(false)

/**
 * 步骤条当前激活步骤
 */
const activeStep = ref(0)

/**
 * 订单跟踪步骤数据
 */
const orderTrackSteps = ref<OrderTrackStep[]>([])

/**
 * 检测当前页面是否为 tabbar 页面
 */
const isTabbarPage = ref(false)

// 自定义最小备餐时间（分钟）- 可以根据需求调整
const minPreparationMinutes = 1

/**
 * 售后商品表格列配置
 */
const afterSaleColumns = ref<TableColumn[]>([
  {
    prop: 'itemName',
    label: '商品名称',
    width: '268rpx',
    align: 'center',
    ellipsis: 3,
    // ellipsis: true,
  },
  {
    prop: 'itemNum',
    label: '数量',
    align: 'center',
  },
  {
    prop: 'payTotalAmount',
    label: '金额',
    width: '150rpx',
    align: 'center',
    formatter: (row, column, cellValue) => {
      const amount = cellValue || 0
      return typeof amount === 'number' ? `¥${amount.toFixed(2)}` : `¥${amount}`
    },
  },
])

/**
 * 获取当前页面路径并判断是否为 tabbar 页面
 */
const checkTabbarPage = () => {
  try {
    const pages = getCurrentPages()
    if (pages && pages.length > 0) {
      const currentPage = pages[pages.length - 1]
      const currentRoute = currentPage.route || ''

      // 定义 tabbar 页面路径列表（根据实际项目的 tabbar 配置）
      const tabbarPages = [
        'pages/index/index',
        'pages/about/about',
        'pages/dishManagement/index',
        'pages/my/index',
        'pages/storeDetail/index',
      ]

      isTabbarPage.value = tabbarPages.some((path) => currentRoute.includes(path))
    }
  } catch (error) {
    console.error('检测 tabbar 页面失败:', error)
    isTabbarPage.value = false
  }
}

// 用于强制更新的计数器
const timerCount = ref(0)
// 定时器引用
let timer: number | null = null
const DISTANCE_CACHE_PREFIX = 'distance_cache_'
// 从localStorage获取距离
const getDistanceFromCache = (orderId: string): string | null => {
  try {
    const cacheKey = `${DISTANCE_CACHE_PREFIX}${orderId}`
    const cacheDataStr = uni.getStorageSync(cacheKey)
    if (cacheDataStr) {
      try {
        // 将JSON字符串转回对象
        const cacheData = JSON.parse(cacheDataStr)
        // 缓存数据格式: { distance: string, timestamp: number }
        return cacheData.distance
      } catch (parseError) {
        console.error('解析缓存数据失败:', parseError)
        return null
      }
    }
    return null
  } catch (error) {
    console.error('获取距离缓存失败:', error)
    return null
  }
}

// 将距离保存到localStorage，同时保存当前时间戳
const saveDistanceToCache = (orderId: string, distance: string): void => {
  try {
    const cacheKey = `${DISTANCE_CACHE_PREFIX}${orderId}`
    const cacheData = {
      distance,
      timestamp: Date.now(), // 保存当前时间戳
    }
    // 将对象转换为JSON字符串存储
    uni.setStorageSync(cacheKey, JSON.stringify(cacheData))
  } catch (error) {
    console.error('保存距离缓存失败:', error)
  }
}

// 从localStorage删除距离缓存
const removeDistanceFromCache = (orderId: string): void => {
  try {
    const cacheKey = `${DISTANCE_CACHE_PREFIX}${orderId}`
    uni.removeStorageSync(cacheKey)
  } catch (error) {
    console.error('删除距离缓存失败:', error)
  }
}

// 在组件挂载时启动定时器
onMounted(() => {
  // 检测当前页面是否为 tabbar 页面
  checkTabbarPage()

  // 每秒更新一次计数器，触发重新渲染
  timer = setInterval(() => {
    timerCount.value++
  }, 1000) as unknown as number

  // 尝试清理过期的距离缓存
  // 注意：这里只是在组件挂载时进行清理
  // 实际应用中，应该在App启动时进行一次性清理
})

// 在组件卸载时清除定时器和缓存
onUnmounted(() => {
  if (timer !== null) {
    clearInterval(timer)
    timer = null
  }

  // 清理当前订单的缓存
  // if (props.orderData && props.orderData.id) {
  //   removeDistanceFromCache(props.orderData.id)
  // }
})

/**
 * 打开订单跟踪弹窗
 */
const openTrackPopup = async () => {
  if (!props.orderData) return

  // 异步获取订单跟踪数据
  try {
    const res = await fetchOrderStatusTime({ thirdOrderCode: props.orderData.channelOrderNo })
    // 假设返回数据结构为 { data: [{ title, time, status, description }, ...] }
    if (res && Array.isArray(res.data)) {
      orderTrackSteps.value = res.data.map((step: any, index: number) => ({
        title: step.statusName,
        time: step.statusTime,
        // 最后一个步骤为process状态（品牌色），其他为finished状态（灰色）
        status: index === res.data.length - 1 ? 'process' : 'finished',
        description: step.description || '',
      }))
      // 设置激活步骤为最后一个
      activeStep.value = orderTrackSteps.value.length - 1
    } else {
      orderTrackSteps.value = []
      activeStep.value = 0
    }
  } catch (e) {
    orderTrackSteps.value = []
    activeStep.value = 0
  }

  // 显示弹窗
  showTrackPopup.value = true
  // 触发事件
  emit('track-order', props.orderData.id)
}

/**
 * 格式化已用时间
 * @param confirmTime 商家接单/确认时间
 */
function formatUsedTime(confirmTime?: string): string {
  if (!confirmTime) return '00:00:00'

  try {
    const confirmDate = new Date(confirmTime)
    const now = new Date()

    // 使用timerCount强制依赖更新，但不直接使用它的值
    const _ = timerCount.value // 使用变量捕获值，避免ESLint错误

    // 计算时间差（毫秒）
    const diffMs = now.getTime() - confirmDate.getTime()

    // 转换为时分秒格式
    const hours = Math.floor(diffMs / (1000 * 60 * 60))
    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60))
    const seconds = Math.floor((diffMs % (1000 * 60)) / 1000)

    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
  } catch (error) {
    console.error('格式化时间失败:', error)
    return '00:00:00'
  }
}

function getPreviousHour(dateTime): Date {
  const result = new Date(dateTime)
  result.setHours(result.getHours() - 1) // 减去1小时
  return result
}

// 获取当前时间开始即时计时并格式化
const startTime = (sendTime?: string) => {
  if (!sendTime) return '00:00:00'

  try {
    // 计算备餐开始时间（送达时间前60分钟）
    const preparationStartTime = new Date(getPreviousHour(sendTime))
    const deliveryTime = new Date(sendTime)
    const now = new Date()

    // 使用timerCount强制依赖更新，但不直接使用它的值
    const _ = timerCount.value // 使用变量捕获值，避免ESLint错误

    // 正在备餐中，计算从备餐开始到现在的正向时间
    const elapsedMs = now.getTime() - preparationStartTime.getTime()
    const totalSeconds = Math.floor(elapsedMs / 1000)
    const hours = Math.floor(totalSeconds / 3600)
    const minutes = Math.floor((totalSeconds % 3600) / 60)
    const seconds = totalSeconds % 60

    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
  } catch (error) {
    console.error('格式化时间失败:', error)
    return '00:00:00'
  }
}

// 监听外部传入的expanded属性变化，但不触发事件
watch(
  () => props.expanded,
  (newValue) => {
    if (typeof newValue === 'boolean') {
      isExpanded.value = newValue
    }
  },
)

// 本地存储计算后的距离
const calculatedDistance = ref<string>('')

// 监听orderData属性变化
watch(
  () => props.orderData,
  (newValue) => {
    if (!newValue) return

    try {
      // 使用类型断言处理实际数据
      const orderDataAny = newValue as any

      // 解析订单详情
      const extlOrderDetail = orderDataAny.extlOrderDetail
        ? typeof orderDataAny.extlOrderDetail === 'string'
          ? JSON.parse(orderDataAny.extlOrderDetail)
          : orderDataAny.extlOrderDetail
        : null

      if (orderDataAny.shopId && orderDataAny.orderLatitude && orderDataAny.orderLongitude) {
        const shopStore = useShopStore()
        const shopDtoList = (shopStore.currentShop?.raw as any)?.shopDtoList || []
        const matchedShop = shopDtoList.find((shop: any) => shop.id === orderDataAny.shopId)

        if (
          matchedShop?.latitude &&
          matchedShop?.longitude &&
          orderDataAny.orderLatitude &&
          orderDataAny.orderLongitude
        ) {
          // 检查是否有订单ID
          const orderId = orderDataAny.id
          if (!orderId) {
            return
          }

          // 检查localStorage中是否已有该订单的距离数据
          const cachedDistance = getDistanceFromCache(orderId)

          if (cachedDistance) {
            // 如果有缓存，直接使用缓存的距离
            calculatedDistance.value = cachedDistance
            return
          }

          // 检查是否有有效的经纬度数据
          if (
            !matchedShop.longitude ||
            !matchedShop.latitude ||
            !orderDataAny.orderLongitude ||
            !orderDataAny.orderLatitude
          ) {
            return
          }

          // 使用高德地图API获取骑行距离
          const aMapKey = 'f868f2c604fb134dfa4be08be595d729' // 替换为实际的API密钥
          const origin = `${matchedShop.longitude},${matchedShop.latitude}`

          // 确保我们能从订单数据中获取到目的地坐标
          let destinationLat = orderDataAny.orderLatitude
          let destinationLng = orderDataAny.orderLongitude

          // 尝试从扩展数据中获取更精确的坐标
          if (extlOrderDetail && extlOrderDetail.syncThirdOrderReqDto) {
            if (extlOrderDetail.syncThirdOrderReqDto.latitude) {
              destinationLat = extlOrderDetail.syncThirdOrderReqDto.latitude
            }
            if (extlOrderDetail.syncThirdOrderReqDto.longitude) {
              destinationLng = extlOrderDetail.syncThirdOrderReqDto.longitude
            }
          }

          const destination = `${destinationLng},${destinationLat}`
          const url = `https://restapi.amap.com/v3/direction/walking?key=${aMapKey}&origin=${origin}&destination=${destination}`

          // 不计算默认值，直接设置为空字符串
          let formattedDistance = ''

          // 存储到本地变量
          calculatedDistance.value = formattedDistance
          // 先用直线距离存入localStorage缓存
          saveDistanceToCache(orderId, formattedDistance)

          // 发起API请求获取更准确的骑行距离
          uni.request({
            url,
            method: 'GET',
            success: (res) => {
              try {
                // 检查API返回是否成功
                const responseData = res.data as any

                if (
                  responseData.route &&
                  responseData.route.paths &&
                  responseData.route.paths.length > 0
                ) {
                  // 获取骑行距离（米）
                  const apiDistance = responseData.route.paths[0].distance

                  if (apiDistance && !isNaN(apiDistance)) {
                    // 格式化API返回的距离
                    formattedDistance =
                      apiDistance < 1000
                        ? `${Math.round(apiDistance)}m`
                        : `${(apiDistance / 1000).toFixed(1)}km`

                    // 更新本地变量
                    calculatedDistance.value = formattedDistance
                    // 更新localStorage缓存
                    saveDistanceToCache(orderId, formattedDistance)
                  }
                }
              } catch (apiError) {
                console.log('解析API返回数据出错:', apiError)
                // 出错时保持使用直线距离，缓存已经在之前设置
              }
            },
            fail: (err) => {
              console.log('请求高德地图API失败:', err)
              // 请求失败时保持使用直线距离，缓存已经在之前设置
            },
          })
        }
      }
    } catch (error) {
      console.log('计算距离时出错:', error)
    }
  },
  { deep: true, immediate: true }, // 使用深度监听以捕获嵌套属性的变化，并立即执行一次
)

/**
 * 打印订单数据，辅助调试
 */
onMounted(() => {})

/**
 * 获取渠道按钮样式
 */
const getChannelButtonStyle = (channel: string) => {
  return channelColorMap[channel] || channelColorMap.default
}

/**
 * 计算商品合计金额
 */
const calculateProductTotal = () => {
  if (!props.orderData || !props.orderData.fees) {
    return 0
  }
  // 参考index.js中的计算方式：商品合计 = 订单总额 - 配送费 - 餐盒费
  const totalAmount = props.orderData.fees.totalAmount || 0
  const deliveryFee = props.orderData.fees.deliveryFee || 0
  const packagingFee = props.orderData.fees.packagingFee || 0
  return totalAmount - deliveryFee - packagingFee
}

/**
 * 格式化金额显示
 */
const displayNum = (num: number): string => {
  // 参考index.js中的displayNum函数
  return typeof num === 'number' ? num.toFixed(2) : '0.00'
}

/**
 * 计算订单应付金额
 */
const calculatePayAmount = () => {
  if (!props.orderData || !props.orderData.fees) {
    return 0
  }
  return props.orderData.fees.payAmount || 0
}

/**
 * 计算实际优惠总额
 */
const calculateRealDiscount = () => {
  if (!props.orderData || !props.orderData.fees) {
    return 0
  }
  // 实际优惠总额 = 优惠金额
  return props.orderData.fees.discountAmount || 0
}

/**
 * 切换展开状态
 */
const toggleExpand = () => {
  isExpanded.value = !isExpanded.value
  // 触发展开状态改变事件
  emit('expand-toggle', isExpanded.value)
}

/**
 * 格式化手机号，只显示尾号4位
 */
const formatPhoneNumber = (phone: string): string => {
  if (!phone || phone.length < 4) return phone || ''
  return phone.slice(-4)
}

/**
 * 格式化订单时间
 */
const formatOrderTime = (time: string): string => {
  if (!time) return time

  try {
    const date = new Date(time)
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const hours = date.getHours().toString().padStart(2, '0')
    const minutes = date.getMinutes().toString().padStart(2, '0')

    return `${month}-${day} ${hours}:${minutes}`
  } catch (error) {
    console.error('格式化时间失败:', error)

    // 如果时间包含T，按ISO格式处理
    if (time.includes('T')) {
      const [datePart, timePart] = time.split('T')
      if (datePart && timePart) {
        const [year, month, day] = datePart.split('-')
        const timeValue = timePart.substring(0, 5)
        if (month && day && timeValue) {
          return `${month}-${day} ${timeValue}`
        }
      }
    }

    // 如果是标准格式，提取月日和时间
    if (time.includes('-') && time.includes(':')) {
      const parts = time.split(' ')
      if (parts.length === 2) {
        const dateParts = parts[0].split('-')
        if (dateParts.length >= 3) {
          const month = dateParts[1]
          const day = dateParts[2]
          const timeValue = parts[1].substring(0, 5)
          return `${month}-${day} ${timeValue}`
        }
      }
    }

    return time
  }
}

/**
 * 检查是否可以拨打电话
 */
const canMakePhoneCall = computed(() => {
  if (!props.orderData) return false

  // 订单已取消时不可拨打电话
  if (props.orderData.orderStatus?.code === 'CANCEL') {
    return false
  }

  // 检查订单是否完成超过1小时
  if (isOrderCompletedOverOneHour.value) {
    return false
  }

  return true
})

/**
 * 检查订单是否完成超过1小时
 */
const isOrderCompletedOverOneHour = computed(() => {
  if (!props.orderData) return false

  // 检查订单状态是否为已完成（状态码：CONFIRM）
  const completedStatuses = ['CONFIRM']
  const orderStatus = props.orderData.orderStatus?.code

  if (!orderStatus || !completedStatuses.includes(orderStatus)) {
    return false
  }

  // 获取完成时间 - 使用 updateTime
  const completedTime = props.orderData.times?.updateTime

  if (!completedTime) return false

  try {
    const completedDate = new Date(completedTime)
    const now = new Date()
    const diffInHours = (now.getTime() - completedDate.getTime()) / (1000 * 60 * 60)

    return diffInHours > 1
  } catch (error) {
    console.error('时间解析失败:', error)
    return false
  }
})

/**
 * 拨打电话事件处理
 */
const onCallClick = () => {
  if (!props.orderData || !props.orderData.customer) return

  // 检查是否可以拨打电话
  if (!canMakePhoneCall.value) {
    uni.showToast({
      title: '为保护客户隐私，电话已不可拨打',
      icon: 'none',
      duration: 2000,
    })
    return
  }

  const phoneNumber = props.orderData.customer.fullPhone || props.orderData.customer.phone || ''
  if (!phoneNumber) return

  // 检测是否为隐私号码格式（包含下划线或逗号）
  if (isPrivacyPhone(phoneNumber)) {
    // 隐私号码格式，显示弹窗提示
    showCallCustomerPopup.value = true
    return
  }
  console.log('phoneNumber', phoneNumber)
  // 普通号码直接拨打
  uni.makePhoneCall({
    phoneNumber,
    success: () => {
      console.log('拨打电话成功')
    },
    fail: (err) => {
      console.error('拨打电话失败:', err)
      // 拨打失败时，仍然触发 call 事件，让父组件可以处理
      emit('call', phoneNumber)
    },
  })
}

/**
 * 拨打骑手电话事件处理
 */
const onCallRiderClick = () => {
  if (!props.orderData || !props.orderData.deliveryRecord) return

  // 检查是否可以拨打电话
  if (!canMakePhoneCall.value) {
    uni.showToast({
      title: '为保护客户隐私，电话已不可拨打',
      icon: 'none',
      duration: 2000,
    })
    return
  }

  // 优先显示弹窗，让用户选择拨打哪个号码
  if (hasRiderContactInfo()) {
    showCallRiderPopup.value = true
    return
  }

  // 如果没有详细联系信息，直接拨打courierMobile
  const phoneNumber = props.orderData.deliveryRecord.courierMobile || ''
  if (!phoneNumber) return

  // 使用 uni.makePhoneCall 拨打电话
  uni.makePhoneCall({
    phoneNumber,
    success: () => {
      console.log('拨打骑手电话成功')
    },
    fail: (err) => {
      console.error('拨打骑手电话失败:', err)
      // 拨打失败时，仍然触发 call 事件，让父组件可以处理
      emit('call', phoneNumber)
    },
  })
}

/**
 * 检查是否有骑手联系信息
 */
const hasRiderContactInfo = (): boolean => {
  const deliveryRecord = props.orderData?.deliveryRecord
  if (!deliveryRecord?.deliveryLogDetailList?.length) return false

  const firstLog = deliveryRecord.deliveryLogDetailList[0]
  return !!(firstLog?.name && firstLog?.mobile)
}

/**
 * 获取骑手手机号
 */
const getRiderMobile = (): string => {
  const deliveryRecord = props.orderData?.deliveryRecord
  if (!deliveryRecord?.deliveryLogDetailList?.length) {
    return deliveryRecord?.courierMobile || ''
  }

  return deliveryRecord.deliveryLogDetailList[0]?.mobile || deliveryRecord?.courierMobile || ''
}

/**
 * 格式化骑手手机号显示（使用通用隐私号方法）
 * @param mobile 原始手机号，可能包含分机号如 "13045975938-411" 或其他隐私号格式
 * @returns 格式化后的显示文本如 "13045975938转411"
 */
const formatRiderPhone = (mobile: string): string => {
  if (!mobile) return ''

  // 使用通用隐私号格式化方法
  return formatPrivacyPhone(mobile)
}

/**
 * 从弹窗中拨打骑手电话
 */
const onCallRiderFromPopup = () => {
  // 检查是否可以拨打电话
  if (!canMakePhoneCall.value) {
    uni.showToast({
      title: '为保护客户隐私，电话已不可拨打',
      icon: 'none',
      duration: 2000,
    })
    showCallRiderPopup.value = false
    return
  }

  const mobile = getRiderMobile()
  if (!mobile) return

  // 提取主要手机号（使用通用隐私号方法）
  const mainPhone = getPrivacyPhoneMain(mobile)
  if (!mainPhone) return

  // 关闭弹窗
  showCallRiderPopup.value = false

  // 使用 uni.makePhoneCall 拨打电话
  uni.makePhoneCall({
    phoneNumber: mainPhone,
    success: () => {
      console.log('拨打骑手电话成功')
    },
    fail: (err) => {
      console.error('拨打骑手电话失败:', err)
      // 拨打失败时，仍然触发 call 事件，让父组件可以处理
      emit('call', mainPhone)
    },
  })
}

/**
 * 格式化隐私号显示（使用通用方法）
 * @param phoneNumber 隐私号如 "13148112418_0778" 或 "13148112418,0778" 或 "13045975938-411"
 * @returns 格式化后的显示文本如 "13148112418转0778"
 */
const formatCustomerPrivacyPhone = (phoneNumber: string): string => {
  return formatPrivacyPhone(phoneNumber)
}

/**
 * 获取隐私号的分机号（使用通用方法）
 * @param phoneNumber 隐私号如 "13148112418_0778" 或 "13148112418,0778" 或 "13045975938-411"
 * @returns 分机号如 "0778"
 */
const getCustomerPhoneExtension = (phoneNumber: string): string => {
  return getPrivacyPhoneExtension(phoneNumber)
}

/**
 * 从弹窗中拨打隐私号客户电话
 */
const onCallCustomerFromPopup = () => {
  // 检查是否可以拨打电话
  if (!canMakePhoneCall.value) {
    uni.showToast({
      title: '为保护客户隐私，电话已不可拨打',
      icon: 'none',
      duration: 2000,
    })
    showCallCustomerPopup.value = false
    return
  }

  if (!props.orderData || !props.orderData.customer) return

  const phoneNumber = props.orderData.customer.fullPhone || props.orderData.customer.phone || ''
  if (!phoneNumber || !isPrivacyPhone(phoneNumber)) return

  // 提取主号码部分（使用通用方法）
  const mainPhone = getPrivacyPhoneMain(phoneNumber)
  if (!mainPhone) return

  // 关闭弹窗
  showCallCustomerPopup.value = false

  // 使用 uni.makePhoneCall 拨打电话
  uni.makePhoneCall({
    phoneNumber: mainPhone,
    success: () => {
      console.log('拨打隐私号客户电话成功')
    },
    fail: (err) => {
      console.error('拨打隐私号客户电话失败:', err)
      // 拨打失败时，仍然触发 call 事件，让父组件可以处理
      emit('call', mainPhone)
    },
  })
}

/**
 * 获取骑手名称
 */
const getRiderName = () => {
  if (!props.orderData?.deliveryRecord?.deliveryLogDetailList?.length) {
    return props.orderData?.deliveryRecord?.courierName || ''
  }
  return props.orderData.deliveryRecord.deliveryLogDetailList[0].name || ''
}

/**
 * 获取送达时间和状态
 */
const getDeliveryTimeAndStatus = () => {
  if (!props.orderData?.deliveryRecord?.deliveryLogDetailList?.length) {
    return props.orderData?.deliveryRecord?.arriveStoreTime || ''
  }

  const logDetail = props.orderData.deliveryRecord.deliveryLogDetailList[0]
  if (!logDetail.time) return ''

  try {
    const date = new Date(logDetail.time)
    const hours = date.getHours().toString().padStart(2, '0')
    const minutes = date.getMinutes().toString().padStart(2, '0')
    const status = logDetail.status || ''
    return `${hours}:${minutes} ${status}`
  } catch (error) {
    console.error('格式化配送时间失败:', error)
    return logDetail.time || ''
  }
}

/**
 * 地址点击事件处理
 */
const onAddressClick = () => {
  if (!props.orderData || !props.orderData.address) return
  emit('address-click', props.orderData.address.detail || '')
}

/**
 * 判断是否显示接单按钮
 */
const showAcceptButton = computed(() => {
  // 待接单状态
  return props.orderData?.orderStatus?.code === 'PAYED'
})

/**
 * 判断是否显示出餐完成按钮
 */
const showFinishButton = computed(() => {
  // 待发配送状态
  return props.orderData?.orderStatus?.code === 'WAIT_CELL_DELIVERY'
})

/**
 * 判断是否显示上报出餐按钮
 */
const showReportButton = computed(() => {
  // 待出餐状态
  return (
    props.orderData?.status === '待出餐' ||
    props.orderData?.orderStatus?.text === '待出餐' ||
    props.orderData?.orderStatus?.code === 'WAIT_REPORT_MEALS' ||
    props.orderData?.apiData?.orderStatus === 'WAIT_REPORT_MEALS' ||
    isNearlyOrder.value
  )
})

/**
 * 判断上报出餐按钮是否可点击
 * 条件：非预定单 或者 (是预定单 且 已经备餐超过N分钟)
 */
const canReportMeals = computed(() => {
  // 使用timerCount依赖，确保每秒都重新计算一次
  const _ = timerCount.value

  // 如果是预订单，并且距离当前时间大于一个小时则不能点击上报出餐
  if (props.orderData?.isBook === 2) {
    const ifOutOneHour = isOutOneHour(props.orderData?.sendTime)
    if (ifOutOneHour) {
      return true
    }
    return false
  }

  // 是预订单，需要检查备餐时间
  const confirmTime = props.orderData?.merchantConfirmTime || props.orderData?.times?.acceptTime
  if (!confirmTime) {
    return false
  }

  try {
    // 计算备餐已用时间（毫秒）
    const confirmDate = new Date(confirmTime)
    const now = new Date()
    const diffMs = now.getTime() - confirmDate.getTime()

    // 转换为分钟
    const diffMinutes = diffMs / (1000 * 60)

    // // 自定义最小备餐时间（分钟）- 可以根据需求调整
    // const minPreparationMinutes = 1

    // 如果备餐时间小于最小要求，按钮不可点击
    return diffMinutes >= minPreparationMinutes
  } catch (error) {
    console.error('计算备餐时间失败:', error)
    return false
  }
})

/**
 * 判断是否为外卖订单（需要配送）
 * bizType != '20' 表示外卖订单，bizType == '20' 表示自提订单
 */
const isDeliveryOrder = computed(() => {
  return props.orderData?.bizType !== '20'
})

/**
 * 判断是否隐藏订单状态信息
 * 自营渠道（微信、支付宝、抖音随心团）需要隐藏订单状态信息
 */
const shouldHideOrderStatus = computed(() => {
  const channel = props.orderData?.saleChannel || ''
  return ['TikTokMiniProgram', 'AlipayMiniProgram', 'WeChatMiniProgram'].includes(channel)
})

/**
 * 判断是否应该显示出餐用时信息
 * 条件：订单状态为备餐完成、已接单、待发配送、待骑手接单、待取餐、配送中等状态
 *
 * 显示逻辑：
 * - 如果 orderData.orderStatus.mealReadyDuration 有值：显示"已上报出餐完成，出餐用时 XX"
 * - 如果 orderData.orderStatus.mealReadyDuration 无值：显示"未上报出餐完成，无出餐用时"
 */
const shouldShowMealTime = computed(() => {
  const statusCode = props.orderData?.orderStatus?.code || ''

  // 支持显示出餐用时的状态列表
  const mealTimeStatuses = [
    'WAIT_CELL_DELIVERY', // 待发配送（备餐完成）
    'WAIT_RIDER_ACCEPT', // 待骑手接单
    'WAIT_PICKUP', // 待取餐
    'DELIVERING', // 配送中
    'WAIT_DELIVERY', // 备餐完成
    'ACCEPTED', // 已接单
    'CONFIRM', // 已接单（确认）
  ]

  return mealTimeStatuses.includes(statusCode)
})

/**
 * 判断是否需要显示"待骑手接单"信息
 * 条件：状态为备餐完成或已接单，且没有配送信息，且为外卖订单
 */
const shouldShowWaitingRiderInfo = computed(() => {
  // 如果不是外卖订单，则不显示
  if (!isDeliveryOrder.value) {
    return false
  }

  // 如果已经有配送信息，则不显示
  if (props.orderData?.deliveryRecord) {
    return false
  }

  // 检查订单状态码
  const statusCode = props.orderData?.orderStatus?.code || ''
  const statusText = props.orderData?.orderStatus?.text || props.orderData?.status || ''

  // 当状态为备餐完成或已接单时显示
  const isWaitingRiderStatus = [
    'ACCEPTED', // 已接单
    'WAIT_DELIVE', // 备餐完成
  ].includes(statusCode)

  return isWaitingRiderStatus
})

/**
 * 判断是否需要隐藏配送信息
 * 当订单状态是完成（CONFIRM）且配送状态是待发起配送、待分配骑手时，隐藏配送信息
 */
const shouldHideDeliveryInfo = computed(() => {
  const orderStatusCode = props.orderData?.orderStatus?.code || ''
  const deliveryStatus = String(props.orderData?.deliveryRecord?.status || '')
  const deliveryStatusName = props.orderData?.deliveryRecord?.statusName || ''

  // 订单状态必须是完成状态
  if (orderStatusCode !== 'CONFIRM') {
    return false
  }

  // 检查配送状态是否为待发起配送或待分配骑手
  const shouldHideStatuses = [
    '待发起配送',
    '待分配骑手',
    'WAIT_DISPATCH', // 状态码：待发起配送
    'WAIT_ASSIGN', // 状态码：待分配骑手
  ]

  return (
    shouldHideStatuses.includes(deliveryStatus) || shouldHideStatuses.includes(deliveryStatusName)
  )
})

/**
 * 判断是否显示退款按钮
 */
const showRefundButton = computed(() => {
  // 获取订单渠道和状态
  const channel = props.orderData?.saleChannel || ''
  const statusCode = props.orderData?.orderStatus?.code || ''
  const status = props.orderData?.status || ''
  // 0. 渠道为支付宝、微信 表示自营 订单状态为 已接单、已完成、待配送（外卖单）、取消配送
  if (['AlipayMiniProgram', 'WeChatMiniProgram'].includes(channel)) {
    return (
      ['ACCEPTED', 'CONFIRM', 'WAITING_DELIVERY', 'CANCEL_DELIVERY'].includes(statusCode) ||
      ['已接单', '已完成', '待配送', '取消配送'].includes(status)
    )
  }
  // 1. 渠道为美团、饿了么时，订单状态为 已接单、已完成、待配送（外卖单）
  if (['MeiTuanTakeOutBrand', 'ELeMeTakeOutBrand'].includes(channel)) {
    return (
      ['ACCEPTED', 'CONFIRM', 'WAITING_DELIVERY'].includes(statusCode) ||
      ['已接单', '已完成', '待配送'].includes(status)
    )
  }

  // 2. 渠道为抖音随心团、抖音小程序时，订单状态为 已接单、已完成、待配送（外卖单）
  if (['TikTokMiniProgram'].includes(channel)) {
    return (
      ['ACCEPTED', 'CONFIRM', 'WAITING_DELIVERY'].includes(statusCode) ||
      ['已接单', '已完成', '待配送'].includes(status)
    )
  }

  // 3. 渠道为京东时，订单状态为 已完成
  if (channel === 'JingDongTakeOutBrand') {
    return statusCode === 'CONFIRM' || status === '已完成'
  }

  // 4. 渠道为抖音小时达时，订单状态为 已接单、待配送（外卖单）、配送中（外卖单）
  if (channel === 'DouYinXiaoshiDa') {
    return (
      ['ACCEPTED', 'WAITING_DELIVERY', 'DELIVERY'].includes(statusCode) ||
      ['已接单', '待配送', '配送中'].includes(status)
    )
  }

  // 默认不显示退款按钮
  return false
})

/**
 * 接单按钮点击事件
 */
const onAcceptOrderClick = async () => {
  if (!props.orderData) return

  try {
    // 获取渠道单号、订单渠道、门店ID和租户ID
    const thirdOrderCode = props.orderData.channelOrderNo
    const channelCode = props.orderData.saleChannel
    const shopId = props.orderData.merchant?.id
    const tenantId = props.orderData.tenantId

    if (!thirdOrderCode || !channelCode || !shopId || !tenantId) {
      uni.showToast({
        title: '订单信息不完整，无法接单',
        icon: 'none',
      })
      // 向外部报告操作结果
      emit('operation-result', {
        action: 'accept',
        success: false,
        message: '订单信息不完整，无法接单',
      })
      return
    }

    // 显示加载提示
    uni.showLoading({
      title: '接单中...',
      mask: true,
    })

    // 调用确认接单接口
    const result = await confirmOrder({
      thirdOrderCode,
      channelCode,
      shopId,
      tenantId,
    })

    if (result && result.resultCode === '0') {
      uni.showToast({
        title: '接单成功',
        icon: 'success',
      })
      // 向外部报告操作结果
      emit('operation-result', {
        action: 'accept',
        success: true,
        message: '接单成功',
      })
      // 同时发送事件给父组件
      emit('accept-order', props.orderData.id)
    } else {
      uni.showToast({
        title: result?.resultMsg || '接单失败',
        icon: 'none',
      })
      // 向外部报告操作结果
      emit('operation-result', {
        action: 'accept',
        success: false,
        message: result?.resultMsg || '接单失败',
      })
    }
  } catch (error) {
    console.error('接单失败:', error)
    uni.showToast({
      title: error?.data.resultMsg || '接单失败，请稍后重试',
      icon: 'none',
    })
    // 向外部报告操作结果
    emit('operation-result', {
      action: 'accept',
      success: false,
      message: error?.data.resultMsg || '接单失败，请稍后重试',
    })
  } finally {
    // 隐藏加载提示
    uni.hideLoading()
  }
}

/**
 * 复制渠道单号
 */
const onCopyChannelOrderNo = () => {
  if (!props.orderData || !props.orderData.channelOrderNo) return

  // 使用 uni.setClipboardData 实现复制功能
  uni.setClipboardData({
    data: props.orderData.channelOrderNo,
    success: () => {
      uni.showToast({
        title: '复制成功',
        icon: 'success',
        duration: 2000,
      })
    },
    fail: (err) => {
      console.error('复制失败:', err)
      uni.showToast({
        title: '复制失败',
        icon: 'none',
        duration: 2000,
      })
    },
    complete: () => {
      // 触发事件，让父组件可以处理
      emit('copy-channel-order', props.orderData.channelOrderNo)
    },
  })
}

/**
 * 出餐完成按钮点击事件
 */
const onFinishOrderClick = () => {
  if (!props.orderData) return
  // 通知父组件处理出餐完成逻辑
  emit('finish-order', props.orderData.id)
  // 向外部报告操作结果
  emit('operation-result', {
    action: 'finish',
    success: true,
    message: '已触发出餐完成',
  })
}

/**
 * 上报出餐按钮点击事件
 */
const onReportOrderClick = async () => {
  if (!props.orderData) return

  // 检查按钮是否可点击（预订单且备餐时间不足）
  if (!canReportMeals.value) {
    return
  }

  try {
    // 获取渠道单号和订单渠道
    const thirdOrderCode = props.orderData.channelOrderNo
    const orderChannel = props.orderData.saleChannel

    if (!thirdOrderCode || !orderChannel) {
      uni.showToast({
        title: '订单信息不完整，无法上报出餐',
        icon: 'none',
      })
      // 向外部报告操作结果
      emit('operation-result', {
        action: 'report',
        success: false,
        message: '订单信息不完整，无法上报出餐',
      })
      return
    }

    // 调用上报出餐接口
    const result = await reportMeals({
      thirdOrderCode,
      orderChannel,
    })

    if (result && result.resultCode === '0') {
      uni.showToast({
        title: '上报出餐成功',
        icon: 'success',
      })
      // 向外部报告操作结果
      emit('operation-result', {
        action: 'report',
        success: true,
        message: '上报出餐成功',
      })
      // 同时发送事件给父组件
      emit('report-order', props.orderData.id)
    } else {
      uni.showToast({
        title: result?.resultMsg || '上报出餐失败',
        icon: 'none',
      })
      // 向外部报告操作结果
      emit('operation-result', {
        action: 'report',
        success: false,
        message: result?.resultMsg || '上报出餐失败',
      })
    }
  } catch (error) {
    console.error('上报出餐失败:', error)
    uni.showToast({
      title: (error && error.resultMsg) || '上报出餐失败，请稍后重试',
      icon: 'none',
    })
    // 向外部报告操作结果
    emit('operation-result', {
      action: 'report',
      success: false,
      message: (error && error.resultMsg) || '上报出餐失败，请稍后重试',
    })
  }
}

/**
 * 申请退款按钮点击事件
 */
const onRefundOrderClick = () => {
  if (!props.orderData) return
  console.log('onRefundOrderClick--props.orderData', props.isAfterSaleMode, props.orderData)
  if (!props.isAfterSaleMode && props.orderData.saleChannel === 'DouYinXiaoshiDa') {
    uni.navigateTo({
      url: `/pages/operationGuide/index?type=${'afterSale'}&channle=douyinxiaoshida&status=zhudongtuikuan`,
    })
    return
  }
  emit('refund-order', props.orderData.channelOrderNo)
  // 向外部报告操作结果
  emit('operation-result', {
    action: 'refund',
    success: true,
    message: '已触发申请退款',
  })
}

/**
 * 格式化售后申请时间
 * @param timeString 时间字符串
 */
const formatRefundApplyTime = (timeString?: string): string => {
  if (!timeString) return ''
  return formatOrderTime(timeString)
}

/**
 * 预览图片
 * @param current 当前图片URL
 * @param urls 所有图片URL数组
 */
const previewImage = (current: string, urls: string[]) => {
  uni.previewImage({
    current,
    urls,
    fail: (err) => {
      console.error('预览图片失败:', err)
      uni.showToast({
        title: '预览图片失败',
        icon: 'none',
      })
    },
  })
}

/**
 * 预览售后图片
 * @param current 当前点击的图片URL
 * @param urls 所有图片URL数组
 */
const previewAfterSaleImage = (current: string, urls: string[]) => {
  if (!current || !urls || urls.length === 0) return

  // 使用 Wot Design Uni 的图片预览
  uni.previewImage({
    current,
    urls,
    fail: (err) => {
      console.error('预览售后图片失败:', err)
      uni.showToast({
        title: '预览图片失败',
        icon: 'none',
      })
    },
  })
}

/**
 * 售后同意按钮点击事件
 */
const onAfterSaleApprove = (tradeNo: string, orderData: any) => {
  if (!tradeNo) return

  // 解构 instanceId 和 tenantId
  const { instanceId, tenantId } = orderData || {}

  // 传递 tradeNo、instanceId、tenantId 参数
  emit('after-sale-approve', tradeNo, instanceId, tenantId)

  // 向外部报告操作结果
  emit('operation-result', {
    action: 'after-sale-approve',
    success: true,
    message: '已触发售后同意',
  })
}

/**
 * 售后拒绝按钮点击事件
 */
const onAfterSaleReject = (tradeNo: string, orderData: any) => {
  if (!tradeNo) return

  // 解构 instanceId 和 tenantId
  const { instanceId, tenantId } = orderData || {}

  // 传递 tradeNo、instanceId、tenantId 参数
  emit('after-sale-reject', tradeNo, instanceId, tenantId)

  // 向外部报告操作结果
  emit('operation-result', {
    action: 'after-sale-reject',
    success: true,
    message: '已触发售后拒绝',
  })
}

/**
 * 检查是否有售后数据
 */
const hasAfterSaleData = computed(() => {
  return Boolean(
    props.orderData?.afterSale &&
      Array.isArray(props.orderData.afterSale) &&
      props.orderData.afterSale.length > 0,
  )
})

/**
 * 当前售后记录（最新的一条）
 */
const currentAfterSaleRecord = computed(() => {
  if (!hasAfterSaleData.value) return null
  const records = props.orderData!.afterSale!
  // 返回最新的记录（数组最后一个）
  return records[records.length - 1]
})

/**
 * 所有售后记录
 */
const allAfterSaleRecords = computed(() => {
  if (!hasAfterSaleData.value) return []
  return props.orderData!.afterSale!
})

/**
 * 排序后的售后记录（最新记录在顶部）
 */
const sortedAfterSaleRecords = computed(() => {
  if (!hasAfterSaleData.value) return []
  const records = [...props.orderData!.afterSale!]
  // 倒序排列，最新记录在顶部
  return records.reverse()
})

/**
 * 售后步骤条数据
 */
const afterSaleSteps = computed(() => {
  if (!hasAfterSaleData.value) return []

  return allAfterSaleRecords.value.map((record, index) => ({
    title: record.refundStatusName || record.title || `步骤${index + 1}`,
    time: formatRefundApplyTime(record.createTime),
    status: index === allAfterSaleRecords.value.length - 1 ? 'process' : 'finished',
    description: record.reason || record.refundStatusName || '',
  }))
})

/**
 * 是否显示售后操作按钮 待审核才需要显示
 */
const shouldShowActions = computed(() => {
  return Boolean(
    props.orderData?.afterSale &&
      Array.isArray(props.orderData.afterSale) &&
      props.orderData.afterSale.length > 0 &&
      props.orderData.afterSale[props.orderData.afterSale.length - 1]?.refundStatus === '10',
  )
  // return hasAfterSaleData.value && currentAfterSaleRecord.value
})

/**
 * 格式化创建时间
 */
const formatCreateTime = (timeStr: string | undefined): string => {
  if (!timeStr) return ''

  try {
    const date = new Date(timeStr)
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')

    return `${month}-${day} ${hours}:${minutes}`
  } catch (error) {
    console.warn('时间格式化失败:', error)
    return timeStr
  }
}

/**
 * 格式化时间显示
 */
const formatTime = (timeStr: string | undefined): string => {
  if (!timeStr) return ''
  return formatOrderTime(timeStr)
}

/**
 * 获取步骤描述
 */
const getStepDescription = (record: any, index: number): string => {
  // 返回状态名称作为步骤描述
  return record.refundStatusName || `步骤${index + 1}`
}

/**
 * 格式化价格显示
 */
const formatPrice = (price: number | undefined): string => {
  if (typeof price !== 'number' || isNaN(price)) return '0.00'
  return price.toFixed(2)
}

/**
 * 转换售后商品数据为表格数据
 */
const transformedAfterSalesItems = computed(() => {
  if (!currentAfterSaleRecord.value?.afterSalesItems) {
    return []
  }

  return currentAfterSaleRecord.value.afterSalesItems.map((item) => ({
    itemName: item.itemName || '',
    itemNum: item.itemNum || 0,
    payTotalAmount: formatPrice(item.payTotalAmount),
  }))
})

/**
 * 打开URL链接
 */
const openUrl = (url: string) => {
  if (!url) return

  try {
    // 在小程序中复制链接到剪贴板
    uni.setClipboardData({
      data: url,
      success: () => {
        uni.showToast({
          title: '链接已复制',
          icon: 'success',
        })
      },
    })
  } catch (error) {
    console.warn('打开链接失败:', error)
  }
}

// (预订单)是否即将到时
const isNearlyOrder = computed(() => {
  const isNear = isOutOneHour(props.orderData?.sendTime)
  if (props.orderData?.orderStatus.code === 'IN_PREPARE' && isNear) {
    return true
  }
  return false
})

// 订单状态是否满足显示取消按钮
const isCancelable = computed(() => {
  // console.log('订单状态：', props.orderData?.orderStatus)
  return !(
    props.orderData?.orderStatus.code === 'CONFIRM' ||
    props.orderData?.orderStatus.code === 'CANCEL'
  )
})

// 暴露属性和方法
defineExpose({
  formatPhoneNumber,
  formatOrderTime,
  isExpanded,
  toggleExpand,
  onAcceptOrderClick,
  onCopyChannelOrderNo,
  calculateProductTotal,
  onFinishOrderClick,
  onReportOrderClick,
  onRefundOrderClick,
  onCallRiderClick,
  getChannelButtonStyle,
  openTrackPopup,
  showTrackPopup,
  getRiderName,
  getDeliveryTimeAndStatus,
  showCallRiderPopup,
  hasRiderContactInfo,
  getRiderMobile,
  formatRiderPhone,
  onCallRiderFromPopup,
  // 隐私号客户电话相关方法
  showCallCustomerPopup,
  formatCustomerPrivacyPhone,
  getCustomerPhoneExtension,
  onCallCustomerFromPopup,
  // 安全区相关方法和状态
  isTabbarPage,
  checkTabbarPage,
  // 售后相关方法
  formatRefundApplyTime,
  previewImage,
  previewAfterSaleImage,
  onAfterSaleApprove,
  onAfterSaleReject,
  // 上报出餐相关状态
  canReportMeals,
  // 配送信息显示相关
  isDeliveryOrder,
  shouldShowWaitingRiderInfo,
  shouldHideDeliveryInfo,
  // 出餐用时显示相关
  shouldShowMealTime,
  // 订单状态显示相关
  shouldHideOrderStatus,
  // 拨打电话限制相关
  canMakePhoneCall,
  isOrderCompletedOverOneHour,
})
</script>

<style lang="scss" src="./index.scss" scoped></style>
