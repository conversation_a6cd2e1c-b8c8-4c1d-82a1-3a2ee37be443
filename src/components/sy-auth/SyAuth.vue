<script setup lang="ts">
import { computed } from 'vue'
import { useAuth } from '@/hooks/useAuth'

// 定义插槽的类型
interface SyAuthSlots {
  default: {
    isAllowed: boolean
    onNoPermissionClick: (e?: Event) => void
    disableStyle: Record<string, any>
    disableClass: Record<string, boolean>
  }
  placeholder?: Record<string, never>
}

const props = defineProps<{
  /**
   * 所需的权限码或权限码数组
   */
  code: string | string[]
  /**
   * 权限检查模式
   * 'all' - 需要拥有所有权限 (默认)
   * 'any' - 拥有任意一个权限即可
   */
  mode?: 'all' | 'any'
  /**
   * 当没有权限时是否显示占位内容
   */
  showPlaceholder?: boolean
  /**
   * 权限未加载时是否显示内容
   * true - 显示内容 (乐观显示)
   * false - 隐藏内容 (保守隐藏，默认)
   */
  showOnLoading?: boolean
  /**
   * 权限渲染模式
   * 'hidden' - 无权限时隐藏（默认）
   * 'toast' - 无权限时显示但禁用，点击弹toast
   */
  toastMode?: 'hidden' | 'toast'
  /**
   * 是否自动应用禁用样式
   * true - 自动应用 grayscale(100%) 和 opacity: 0.7 (默认)
   * false - 不自动应用，由业务方自定义
   */
  autoDisableStyle?: boolean
}>()

// 定义插槽
defineSlots<SyAuthSlots>()

const { hasPermission, hasAnyPermission, isPermissionLoaded } = useAuth()

const isAllowed = computed(() => {
  if (!isPermissionLoaded()) {
    return props.showOnLoading || false
  }
  const codes = Array.isArray(props.code) ? props.code : [props.code]
  if (props.mode === 'any') {
    return hasAnyPermission(codes)
  } else {
    return hasPermission(props.code)
  }
})

const renderMode = computed(() => props.toastMode || 'toast')
const autoDisableStyle = computed(() => props.autoDisableStyle !== false) // 默认 true

function onNoPermissionClick(e?: Event) {
  if (e) {
    e.stopPropagation()
    e.preventDefault()
  }
  uni.showToast({
    title: '暂无权限',
    icon: 'none',
    duration: 1500,
  })
}

// 自动应用禁用样式的计算属性
const disableStyle = computed(() => {
  if (!autoDisableStyle.value || isAllowed.value) {
    return {}
  }
  return {
    filter: 'grayscale(100%)',
    opacity: 0.7,
    pointerEvents: 'none' as const,
  }
})

// 自动应用禁用类名的计算属性
const disableClass = computed(() => {
  if (!autoDisableStyle.value || isAllowed.value) {
    return {}
  }
  return {
    'sy-auth-no-permission': true,
  }
})

defineExpose({
  isAllowed,
  onNoPermissionClick,
  disableStyle,
  disableClass,
})

defineOptions({
  name: 'SyAuth',
})
</script>

<template>
  <!-- toast 模式：无权限时依然渲染slot，但禁用/拦截由业务方决定 -->
  <template v-if="renderMode === 'toast'">
    <slot
      :isAllowed="isAllowed"
      :onNoPermissionClick="onNoPermissionClick"
      :disableStyle="disableStyle"
      :disableClass="disableClass"
    />
  </template>
  <!-- hidden 模式：无权限直接不渲染slot -->
  <template v-else>
    <slot v-if="isAllowed" />
    <slot v-else-if="showPlaceholder" name="placeholder">
      <view class="sy-auth-placeholder text-gray-400 text-center py-2">
        <text>权限不足</text>
      </view>
    </slot>
  </template>
</template>

<style lang="scss" scoped>
.sy-auth-no-permission {
  pointer-events: none !important;
  cursor: not-allowed !important;
  user-select: none;
  filter: grayscale(100%);
  opacity: 0.7;
}

.sy-auth-placeholder {
  font-size: 28rpx;
  color: #999;
}
</style>
