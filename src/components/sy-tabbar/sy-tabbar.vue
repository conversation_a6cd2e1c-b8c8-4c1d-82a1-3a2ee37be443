<template>
  <wd-tabbar
    fixed
    v-model="tabbarStore.curIdx"
    bordered
    safeAreaInsetBottom
    placeholder
    custom-style="height:104rpx;diplay:flex;align-items: flex-start;"
    @change="selectTabBar"
  >
    <block v-for="(item, idx) in tabbarList" :key="item.path">
      <wd-tabbar-item
        v-if="item.iconType === 'wot'"
        :title="item.text || ''"
        :icon="item.icon"
      ></wd-tabbar-item>
      <wd-tabbar-item
        v-else-if="item.iconType === 'unocss' || item.iconType === 'iconfont'"
        :title="item.text || ''"
      >
        <template #icon>
          <wd-transition :show="true" name="fade-down">
            <image
              :src="idx === tabbarStore.curIdx ? item.iconSelected : item.icon"
              h-80rpx
              w-80rpx
              :key="idx === tabbarStore.curIdx ? 'selected-' + idx : 'normal-' + idx"
            />
          </wd-transition>
        </template>
      </wd-tabbar-item>
      <wd-tabbar-item
        v-else-if="item.iconType === 'local'"
        :title="item.text || ''"
        custom-style="height:80rpx;padding-top:12rpx"
      >
        <template #icon>
          <wd-transition v-if="idx === tabbarStore.curIdx" :show="true" name="fade">
            <image :src="item.iconSelected" :key="'selected-' + idx" h-80rpx w-80rpx />
          </wd-transition>
          <image v-else :src="item.icon" :key="'normal-' + idx" h-80rpx w-80rpx />
        </template>
      </wd-tabbar-item>
    </block>
  </wd-tabbar>
</template>

<script setup lang="ts">
// unocss icon 默认不生效，需要在这里写一遍才能生效！注释掉也是生效的，但是必须要有！
// i-carbon-code
import { computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { useAuthStore } from '@/store/auth'
import { tabbarStore, tabbarList as fullTabbarList, switchTab } from './sy-tabbar'

// 权限相关逻辑
const authStore = useAuthStore()

/** 根据权限过滤的 TabBar 列表 */
const tabbarList = computed(() => {
  if (!authStore.isPermissionLoaded) {
    return [] // 权限加载中，不显示任何项
  }
  return fullTabbarList
    .filter((item) => {
      // 如果item没有配置permissionCode，则默认展示
      if (!item.permissionCode) return true
      return authStore.hasPermission(item.permissionCode)
    })
    .map((item) => ({
      ...item,
      path: `/${item.pagePath}`,
    }))
})

function selectTabBar({ value: index }: { value: number }) {
  const item = tabbarList.value[index]
  if (item) {
    switchTab(item, index)
  }
}

onLoad(() => {
  // 解决原生 tabBar 未隐藏导致有2个 tabBar 的问题
  // #ifdef APP-PLUS | H5
  uni.hideTabBar({
    fail(err) {
      console.log('hideTabBar fail: ', err)
    },
    success(res) {
      console.log('hideTabBar success: ', res)
    },
  })
  // #endif

  // 根据当前页面路径设置正确的tabbar索引
  const pages = getCurrentPages()
  if (pages.length > 0) {
    const currentPage = pages[pages.length - 1]
    const currentPath = currentPage.route

    // 查找当前路径在tabbar列表中的索引
    const filteredList = tabbarList.value
    const tabIndex = filteredList.findIndex(
      (item) => item.path === `/${currentPath}` || `/${item.pagePath}` === `/${currentPath}`,
    )

    if (tabIndex !== -1 && tabIndex !== tabbarStore.curIdx) {
      tabbarStore.setCurIdx(tabIndex)
    }
  }
})
</script>

<style lang="scss" src="./index.scss" scoped></style>
