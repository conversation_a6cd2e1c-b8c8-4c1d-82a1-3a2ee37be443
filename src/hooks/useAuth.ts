import { useAuthStore } from '@/store/auth'

/**
 * 权限检查的组合式函数
 */
export function useAuth() {
  const authStore = useAuthStore()

  /**
   * 检查是否拥有指定权限
   * @param code - 所需的权限码或权限码数组
   * @returns boolean
   */
  const hasPermission = (code: string | string[]): boolean => {
    console.log('hasPermission', code, authStore.permissionCodes)
    return authStore.hasPermission(code)
  }

  /**
   * 检查是否拥有任意一个权限
   * @param codes - 权限码数组
   * @returns boolean
   */
  const hasAnyPermission = (codes: string[]): boolean => {
    return authStore.hasAnyPermission(codes)
  }

  /**
   * 获取用户的所有权限码
   * @returns string[]
   */
  const getPermissionCodes = (): string[] => {
    return authStore.permissionCodes
  }

  /**
   * 获取用户的按钮权限
   * @returns PermissionItem[]
   */
  const getButtonPermissions = () => {
    return authStore.buttonPermissions
  }

  /**
   * 获取用户的菜单权限
   * @returns PermissionItem[]
   */
  const getMenuPermissions = () => {
    return authStore.menuPermissions
  }

  /**
   * 根据父级权限码获取子权限
   * @param parentCode - 父级权限码
   * @returns PermissionItem[]
   */
  const getChildPermissions = (parentCode: string) => {
    return authStore.getChildPermissions(parentCode)
  }

  /**
   * 加载权限数据
   * @returns Promise<void>
   */
  const fetchPermissions = async (): Promise<void> => {
    await authStore.fetchPermissions()
  }

  /**
   * 重新加载权限数据
   * @returns Promise<void>
   */
  const reloadPermissions = async (): Promise<void> => {
    await authStore.reloadPermissions()
  }

  /**
   * 清理权限数据
   */
  const clearPermissions = (): void => {
    authStore.clearPermissions()
  }

  /**
   * 权限是否已加载
   * @returns boolean
   */
  const isPermissionLoaded = (): boolean => {
    return authStore.isPermissionLoaded
  }

  return {
    // 权限检查
    hasPermission,
    hasAnyPermission,

    // 权限数据获取
    getPermissionCodes,
    getButtonPermissions,
    getMenuPermissions,
    getChildPermissions,

    // 权限管理
    fetchPermissions,
    reloadPermissions,
    clearPermissions,
    isPermissionLoaded,
  }
}
