import { useUserStore } from '@/store/user'
import { setDeviceOffline } from '@/utils/device'
import { ref, onMounted } from 'vue'
import { isApp } from '@/utils/platform'

/**
 * 版本号
 */
export const version = ref('')

/**
 * 获取APP版本号
 */
export const getAppVersion = () => {
  // 只在APP环境下获取版本号
  if (isApp) {
    try {
      // #ifdef APP-PLUS
      plus.runtime.getProperty(plus.runtime.appid, (widgetInfo) => {
        version.value = widgetInfo.version || ''
      })
      // #endif
    } catch (error) {
      console.error('获取APP版本号失败:', error)
      version.value = '未知'
    }
  }
}

// 在组件挂载时获取版本号
onMounted(() => {
  getAppVersion()
})

/**
 * 处理退出登录
 */
export const handleLogout = () => {
  uni.showModal({
    title: '提示',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        // 设置设备离线状态
        setDeviceOffline('用户退出登录')

        // 获取用户状态管理
        const userStore = useUserStore()
        // 清除用户信息
        userStore.clearUserInfo()
        // 跳转到登录页面
        uni.reLaunch({
          url: '/pages/login/detail/index',
        })
      }
    },
  })
}
