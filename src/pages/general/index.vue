<route lang="json5">
{
  style: {
    navigationBarTitleText: '通用设置',
    navigationStyle: 'custom',
    'app-plus': {
      titleNView: false,
    },
  },
}
</route>
<template>
  <view class="general-settings-page bg-#FFFFFF min-h-screen">
    <wd-navbar title="通用设置" left-text="" left-arrow @click-left="handleClickBack" />
    <view class="general-setting">
      <!-- 页面内容区域 -->
      <view class="page-content px-3">
        <!-- 此处其他区域不需要实现 -->
        <SyAuth
          code="jidanbao_menu_storeDetail_messageRing"
          toast-mode="toast"
          v-slot="{ isAllowed, onNoPermissionClick }"
        >
          <wd-cell
            title="消息和铃声设置"
            :class="{ 'sy-auth-no-permission': !isAllowed }"
            :style="{
              filter: !isAllowed ? 'grayscale(100%)' : 'none',
              opacity: !isAllowed ? 0.7 : 1,
            }"
            :is-link="isAllowed"
            :to="isAllowed ? './messageRing/index' : undefined"
            @click="!isAllowed && onNoPermissionClick()"
          />
        </SyAuth>
        <!-- <wd-cell title="餐损申诉" is-link to="../damageAppeal/index" /> -->
        <!--
        <wd-button type="primary" size="small" @click="toStartServer">开启前台服务</wd-button>
        <wd-button type="primary" size="small" @click="toStopServer">关闭前台服务</wd-button> -->
        <!-- <wd-button type="primary" size="small" @click="toCheckNotify">
          检查通知服务权限是否开
        </wd-button>
        <wd-button type="primary" size="small" @click="toNotifySetting">跳转通知授权页面</wd-button>
        <wd-button type="primary" size="small" @click="toBatterySetting">
          跳转电池优化页面
        </wd-button> -->
      </view>
      <!-- 版本号 -->
      <!-- #ifdef APP-PLUS -->
      <view class="version-container">
        <text class="version-text">版本号：{{ version }}</text>
      </view>
      <!-- #endif -->
      <!-- 底部退出登录按钮 -->
      <wd-button
        class="logout-button-container pb-safe"
        type="primary"
        size="large"
        @click="isShowSureModel = true"
        block
      >
        退出登录
      </wd-button>
    </view>

    <!--退出登录确认弹框-->
    <SyPopup
      v-if="isShowSureModel"
      v-model="isShowSureModel"
      title="退出登录"
      confirm-text="确定"
      :show-cancel="true"
      @cancel="isShowSureModel = !isShowSureModel"
      @confirm="handleLogout"
    >
      <text class="sy-popup-txt">是否确认退出登录</text>
    </SyPopup>
  </view>
</template>

<script setup lang="ts">
import { useUserStore } from '@/store/user'
import { setDeviceOffline } from '@/utils/device'
import SyPopup from '@/components/sy-popup'
import SyAuth from '@/components/sy-auth'
import { ref, onMounted } from 'vue'
import { isApp } from '@/utils/platform'

const isShowSureModel = ref(false)
const version = ref('')

/**
 * 获取APP版本号
 */
const getAppVersion = () => {
  // 只在APP环境下获取版本号
  if (isApp) {
    try {
      // #ifdef APP-PLUS
      plus.runtime.getProperty(plus.runtime.appid, (widgetInfo) => {
        version.value = widgetInfo.version || ''
      })
      console.log('version.value', version.value)

      // #endif
    } catch (error) {
      console.error('获取APP版本号失败:', error)
      version.value = '未知'
    }
  }
}

// 在组件挂载时获取版本号
onMounted(() => {
  getAppVersion()
})

const handleClickBack = () => {
  uni.navigateBack()
}

/**
 * 处理退出登录
 */
const handleLogout = () => {
  isShowSureModel.value = false

  // 设置设备离线状态
  setDeviceOffline('用户退出登录')

  // 获取用户状态管理
  const userStore = useUserStore()
  // 清除用户信息
  userStore.clearUserInfo()
  // 跳转到登录页面
  uni.reLaunch({
    url: '/pages/login/detail/index',
  })
}

const toStartServer = () => {
  const testModule = uni.requireNativePlugin('Xkeep-Life-uniplugin_module')
  testModule.init((res) => {
    console.log('保活插件初始化返回：', res)
    const fixedString = res.replace(/'/g, '"').replace(/(\w+):/g, '"$1":')
    const result = JSON.parse(fixedString)
    if (result.isOk) {
      testModule.toStartForegroundService()
    }
  })
}

const toStopServer = () => {
  const testModule = uni.requireNativePlugin('Xkeep-Life-uniplugin_module')
  testModule.toStopForegroundService()
}

const toCheckNotify = () => {
  const testModule = uni.requireNativePlugin('Xkeep-Life-uniplugin_module')
  testModule.isNotificationEnabled((res) => {
    console.log('检查通知权限返回：', res)
    uni.showToast({ title: res.isEnabled ? '已开启通知权限' : '未开启通知权限' })
  })
}

// 跳转通知授权页面
const toNotifySetting = () => {
  const testModule = uni.requireNativePlugin('Xkeep-Life-uniplugin_module')
  testModule.openNotificationSettings()
}

// 跳转电池优化页面
const toBatterySetting = () => {
  const testModule = uni.requireNativePlugin('Xkeep-Life-uniplugin_module')
  testModule.gotoBatteryOptimizationPage()
}
</script>

<style lang="scss" src="./index.scss" scoped></style>
