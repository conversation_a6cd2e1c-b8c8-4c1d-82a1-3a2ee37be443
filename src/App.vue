<script setup lang="ts">
import { onLaunch, onShow, onHide, onUnload } from '@dcloudio/uni-app'
import 'abortcontroller-polyfill/dist/abortcontroller-polyfill-only'
import { useUserStore } from '@/store'
import { needLoginPages } from '@/utils'
import { useDeviceStore } from '@/store/device'
import { useShopStore } from '@/store/shop'
import { saveDeviceAPI } from '@/service/user'

import { setDeviceOffline, setDeviceOnline } from '@/utils/device'
import { handleJPushMessage, MessageType } from '@/utils/jpush'
import { initTTS, playTTSAnnouncement, type TTSPayload } from '@/utils/tts'
import { clearExpiredDistanceCache, getDistanceCacheStats } from '@/utils/cache'
// #ifdef APP-PLUS
const jpushModule = uni.requireNativePlugin && uni.requireNativePlugin('JG-JPush')
// #endif

/**
 * 初始化 MQTT 兼容性补丁
 * 解决 uni.connectSocket 在某些平台上的兼容性问题
 */
function initMqttPolyfill() {
  try {
    // 检查是否存在 uni.connectSocket 方法
    if (!uni.connectSocket) {
      console.warn('uni.connectSocket 方法不存在，跳过 MQTT 兼容性补丁')
      return
    }

    // 保存原始的 connectSocket 方法
    const originalConnectSocket = uni.connectSocket

    // 重写 uni.connectSocket 方法，强制返回 SocketTask
    uni.connectSocket = function (options: UniApp.ConnectSocketOption): UniApp.SocketTask {
      console.log('MQTT兼容性补丁：uni.connectSocket 调用参数:', options)

      // 确保 success 回调存在
      if (!options.success) {
        options.success = function (res: any) {
          console.log('MQTT兼容性补丁：WebSocket 连接成功', res)
        }
      }

      // 确保 fail 回调存在
      if (!options.fail) {
        options.fail = function (err: any) {
          console.error('MQTT兼容性补丁：WebSocket 连接失败', err)
        }
      }

      // 调用原始方法并返回结果
      try {
        const result = originalConnectSocket.call(this, options)
        console.log('MQTT兼容性补丁：uni.connectSocket 调用成功')
        return result
      } catch (error) {
        console.error('MQTT兼容性补丁：uni.connectSocket 调用失败', error)
        throw error
      }
    }

    console.log('MQTT兼容性补丁已应用')
  } catch (error) {
    console.error('应用MQTT兼容性补丁失败:', error)
  }
}

/**
 * 打印MQTT环境诊断信息
 */
function printMqttDiagnostics() {
  console.group('🔍 MQTT环境诊断信息')

  // 平台信息
  const systemInfo = uni.getSystemInfoSync()
  console.log('📱 平台信息:', {
    platform: systemInfo.platform,
    system: systemInfo.system,
    version: systemInfo.version,
  })

  // WebSocket支持情况
  const wsSupport = {
    connectSocket: typeof uni.connectSocket === 'function',
    sendSocketMessage: typeof uni.sendSocketMessage === 'function',
    closeSocket: typeof uni.closeSocket === 'function',
  }
  console.log('🌐 WebSocket支持:', wsSupport)

  console.groupEnd()
}

/**
 * 设置全局事件监听
 */
function setupGlobalEvents() {
  // 监听模拟新订单推送事件
  uni.$on('simulate-order-push', (orderData?: any) => {
    console.log('收到模拟新订单推送事件')
    const mockPushMessage = {
      type: MessageType.NEW_ORDER,
      title: '新订单提醒',
      content: '您有一个新的订单，请及时处理',
      orderId: orderData?.orderId || 'ORDER_' + Date.now(),
      timestamp: Date.now(),
      ...orderData,
    }

    handleJPushMessage(mockPushMessage)
  })

  // 监听TTS内容播放事件
  uni.$on('test-tts-content', (content?: string) => {
    console.log('收到TTS内容播放事件', content)
    if (content) {
      playTTSAnnouncement({ content })
    }
  })

  // 监听直接播放TTS内容事件
  uni.$on('play-tts-content', (content: string) => {
    console.log('收到播放TTS内容事件:', content)
    playTTSAnnouncement({ content })
  })
}

/**
 * 发送本地通知（应用在后台时使用）
 * @param title 通知标题
 * @param content 通知内容
 */
function sendLocalNotification(title: string, content: string) {
  // #ifdef APP-PLUS
  if (!jpushModule) return

  try {
    // 创建本地通知
    const notificationId = Date.now().toString()

    jpushModule.addLocalNotification({
      messageID: notificationId,
      title,
      content,
      extras: {
        type: MessageType.NEW_ORDER,
      },
    })

    console.log('发送本地通知成功:', notificationId)
  } catch (error) {
    console.error('发送本地通知失败:', error)
  }
  // #endif
}

function setupJPush() {
  // #ifdef APP-PLUS
  try {
    if (!jpushModule) return
    const platform = uni.getSystemInfoSync().platform
    if (platform === 'ios') {
      const locationServicesEnabled = jpushModule.locationServicesEnabled()
      const locationAuthorizationStatus = jpushModule.getLocationAuthorizationStatus()
      console.log('locationAuthorizationStatus', locationAuthorizationStatus)
      if (locationServicesEnabled === true && locationAuthorizationStatus < 3) {
        jpushModule.requestLocationAuthorization((result: { status: number }) => {
          console.log('定位权限', result.status)
        })
      }
      jpushModule.requestNotificationAuthorization((result: { status: number }) => {
        if (result.status < 2) {
          // uni.showToast({
          //   icon: 'none',
          //   title: '您还没有打开通知权限',
          //   duration: 3000,
          // })
        }
      })
    }
    jpushModule.initJPushService()

    jpushModule.setLoggerEnable(true)

    jpushModule.addConnectEventListener((result: { connectEnable: boolean }) => {
      console.log('连接状态', result.connectEnable)
      jpushModule.getRegistrationID(async (result: { registerID: string }) => {
        console.log('极光推送 registrationID:', result.registerID)
        // 保存到设备store中
        if (result.registerID) {
          try {
            const deviceStore = useDeviceStore()
            deviceStore.setRegisterId(result.registerID)
            console.log('设备ID已保存到store:', result.registerID)

            // 新增：如果已登录则更新设备在线状态和门店ID
            const userStore = useUserStore()
            // console.log('已登录:', userStore.isLogined)

            if (userStore.isLogined) {
              try {
                // 获取当前选中的门店ID
                const shopStore = useShopStore()
                const currentShopId = shopStore.currentShopId

                console.log('App启动时获取到的门店ID:', currentShopId)

                // 同时更新设备在线状态和门店ID
                await saveDeviceAPI(
                  result.registerID, // deviceId
                  undefined, // phone (不修改)
                  1, // loginStatus (1表示在线)
                  currentShopId || undefined, // loginShopId (如果有选中门店则更新)
                )

                console.log('App启动后已自动设置设备在线状态，门店ID:', currentShopId || '未选择')
              } catch (error) {
                console.error('App启动时更新设备在线状态和门店ID失败:', error)
                // 错误不阻断应用启动流程
              }
            } else {
              try {
                await setDeviceOffline('App启动自动下线')
              } catch (error) {
                console.error('App启动时设置设备离线状态失败:', error)
              }
            }
          } catch (error) {
            console.error('保存设备ID到store或设置在线状态失败:', error)
          }
        }
      })
      uni.$emit && uni.$emit('connectStatusChange', result.connectEnable)
    })

    // 监听通知消息
    jpushModule.addNotificationListener((result: any) => {
      console.log('收到通知消息:', result)

      try {
        // 播放简单的TTS提示
        // playTTSContent('您有新的订单，请及时处理')
        // console.log('通知消息：TTS播放成功')
      } catch (error) {
        console.error('通知消息：TTS播放失败:', error)
      }
    })

    // 监听自定义消息
    jpushModule.addCustomMessageListener((result: any) => {
      console.log('收到自定义消息:', result)

      try {
        let hasPlayedSmartTTS = false

        // 尝试解析结构化数据进行智能播报
        try {
          let extras: any = {}

          // 解析嵌套的 JSON 结构
          if (result.extras && result.extras['message extras key']) {
            console.log('原始 message extras key:', result.extras['message extras key'])

            // 第一层解析：解析 message extras key 字符串
            const messageExtras = JSON.parse(result.extras['message extras key'])
            console.log('解析后的 messageExtras:', messageExtras)

            // 第二层解析：解析 extras 字段
            if (messageExtras.extras) {
              if (typeof messageExtras.extras === 'string') {
                extras = JSON.parse(messageExtras.extras)
              } else {
                extras = messageExtras.extras
              }
            }
          } else if (result.extras) {
            extras = result.extras
          }

          console.log('最终解析的 extras:', extras)

          // notifyType 值映射（处理后端和前端的差异）
          const notifyTypeMap: Record<string, string> = {
            accepted: 'order_accepted',
            user_remind: 'user_remind',
            user_cancel: 'user_cancel',
            abnormal_delivery: 'abnormal_delivery',
            over_time_wait_receive: 'over_time_wait_receive',
            // 可以根据需要添加更多映射
          }

          // 如果解析到结构化数据，使用智能TTS播报
          if (extras.notifyType && result.content) {
            // 构造 payload
            const payload: TTSPayload = {
              content: result.content,
              notifyType: notifyTypeMap[extras.notifyType] || extras.notifyType,
              channel: extras.channel,
              takeNo: extras.takeNo ? parseInt(extras.takeNo) : undefined,
            }

            console.log('构造的 TTS payload:', payload)

            // 使用智能TTS播报（会根据notifyType自动判断前置音乐）
            playTTSAnnouncement(payload)
            console.log('自定义消息：智能TTS播报成功')
            hasPlayedSmartTTS = true
          }
        } catch (parseError) {
          console.warn('解析结构化数据失败，将使用简单内容播报:', parseError)
        }

        // 如果没有成功播放智能TTS，则使用简单内容播报
        if (!hasPlayedSmartTTS && result.content) {
          playTTSAnnouncement({ content: result.content })
          console.log('自定义消息：简单TTS播放成功，内容:', result.content)
        }
      } catch (error) {
        console.error('自定义消息处理失败:', error, result)
      }
    })

    jpushModule.addLocalNotificationListener((result: any) => {
      console.log('收到本地通知:', result)

      try {
        // 播放简单的TTS提示
        // playTTSContent('您有新的本地通知')
        console.log('本地通知：TTS播放成功')
      } catch (error) {
        console.error('本地通知：TTS播放失败:', error)
      }
    })

    // jpushModule.addGeofenceListener((result: any) => {
    // uni.showToast({
    //   icon: 'none',
    //   title: '触发地理围栏',
    //   duration: 3000,
    // })
    // })
    jpushModule.setIsAllowedInMessagePop(true)
    // jpushModule.pullInMessage((result: { code: number }) => {
    //   console.log('pullInMessage', result.code)
    // })
    jpushModule.addInMessageListener((result: any) => {
      console.log('inMessageListener', result.eventType, result.messageType, result.content)

      // 判断是否为新订单消息
      if (
        result &&
        (result.messageType === MessageType.NEW_ORDER || result.content?.includes('new_order'))
      ) {
        // 判断应用是否在前台
        // @ts-expect-error plus.push.getClientInfo 类型定义问题
        plus.push.getClientInfo((info) => {
          const isInForeground = info.running && !info.background

          if (!isInForeground) {
            // 应用在后台，发送本地通知
            // sendLocalNotification('新订单提醒', '您有一个新的订单，请及时处理')
          }

          // 无论前台后台都播放TTS
          // playTTSContent('您有新的订单，请及时处理')
        })
      }
    })
  } catch (error) {
    console.error('Error initializing jpush:', error)
  }
  // #endif
}

onLaunch(async () => {
  console.log('App Launch')

  // 清除之前的应用启动时间，确保每次启动都能检查更新
  try {
    uni.removeStorageSync('__app_launch_time__')
    console.log('已清除之前的应用启动时间')
  } catch (error) {
    console.warn('清除应用启动时间失败:', error)
  }

  // 注意：应用更新检查现在由 layout 中的 SyUpgradeManager 组件处理
  // 每次进入APP都会强制刷新OSS配置并检查更新
  // 获取缓存统计信息
  const stats = getDistanceCacheStats()
  console.log(`当前订单距离缓存数量: ${stats.count}`)
  if (stats.oldestTime) {
    console.log(`最早的缓存时间: ${stats.oldestTime.toLocaleString()}`)
  }

  // 清理超过7天的缓存
  const clearedCount = clearExpiredDistanceCache(7)
  console.log(`已清理 ${clearedCount} 个过期的订单距离缓存`)
  // 初始化 MQTT 兼容性补丁
  initMqttPolyfill()

  // 打印 MQTT 环境诊断信息（开发环境）
  // #ifdef H5
  if (import.meta.env.DEV) {
    printMqttDiagnostics()
  }
  // #endif

  // 初始化TTS插件
  try {
    const ttsInitSuccess = await initTTS()
    if (ttsInitSuccess) {
      console.log('✅ TTS插件初始化成功')
    } else {
      console.warn('⚠️ TTS插件初始化失败')
    }
  } catch (error) {
    console.error('❌ TTS插件初始化异常:', error)
  }

  setupGlobalEvents()

  // 使用 setTimeout 确保在应用完全初始化后再访问 store
  try {
    // Check login status
    const userStore = useUserStore()
    const isLogined = userStore.isLogined
    console.log('App launch - login status:', isLogined)

    // If user is not logged in, set device offline and redirect to login page
    if (!isLogined) {
      // 设置设备离线状态
      setDeviceOffline('未登录状态')

      const pages = getCurrentPages()
      let path = '/'

      if (pages && pages.length > 0) {
        const currentPage = pages[0]
        path = `/${currentPage.route}`
      }

      // Check if current path is a login page
      const isLoginPage = path.includes('/pages/login/')

      // If it's not a login page, redirect to login page
      if (!isLoginPage) {
        console.log('Redirecting to login page from:', path)
        uni.reLaunch({ url: '/pages/login/detail/index' })
      }
    }
  } catch (error) {
    console.error('初始化登录状态检查失败:', error)
  }
})

// 开启前台服务
const toStartServer = () => {
  const testModule = uni.requireNativePlugin('Xkeep-Life-uniplugin_module')
  testModule.init((res) => {
    console.log('保活插件初始化返回：', res)
    const fixedString = res.replace(/'/g, '"').replace(/(\w+):/g, '"$1":')
    const result = JSON.parse(fixedString)
    if (result.isOk) {
      testModule.checkServiceStatus('com.jidanbao.shop.XKeepLifeServer', (res) => {
        console.log('服务状态：', res)
        if (res !== true || res !== 'true') {
          console.log('服务未运行，启动服务')
          testModule.toStartForegroundService()
          testModule.toStartForegroundSunService()
        }
      })
    }
  })
}

onShow(() => {
  console.log('App Show')
  setupJPush()

  // 启动保活服务
  toStartServer()
})

onHide(() => {
  console.log('App Hide')
})

// 应用卸载前清理事件和资源
onUnload(() => {
  console.log('App Unload')
  // 清理事件监听
  uni.$off('simulate-order-push')
  uni.$off('test-tts-content')
  uni.$off('play-tts-content')
})
</script>

<style lang="scss">
/* stylelint-disable selector-type-no-unknown */
button::after {
  border: none;
}

swiper,
scroll-view {
  flex: 1;
  height: 100%;
  overflow: hidden;
}

image {
  width: 100%;
  height: 100%;
  vertical-align: middle;
}

// 单行省略，优先使用 unocss: text-ellipsis
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 两行省略
.ellipsis-2 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

// 三行省略
.ellipsis-3 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
</style>
